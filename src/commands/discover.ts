/**
 * Discover command implementation
 */

import chalk from 'chalk';
import { ConfigManager } from '../config/manager.js';
import { ComponentRegistry } from '../discovery/registry.js';

export async function discoverCommand(options: any) {
  try {
    console.log(chalk.blue('🔍 Discovering components...'));
    
    // Load configuration
    const configManager = new ConfigManager();
    const config = await configManager.loadConfig();
    
    // Merge CLI options with config
    const mergedConfig = {
      ...config,
      discovery: {
        ...config.discovery,
        scanDependencies: options.scanDeps ?? config.discovery.scanDependencies
      }
    };

    // Initialize component registry
    const registry = new ComponentRegistry(mergedConfig.discovery);
    await registry.initialize();
    
    // Get components
    let components = registry.getComponents();
    
    // Apply filters
    if (options.library) {
      components = components.filter(c => c.library === options.library);
    }
    
    if (options.framework) {
      components = components.filter(c => c.framework === options.framework);
    }

    // Display results
    const format = options.format || 'table';
    
    if (format === 'json') {
      console.log(JSON.stringify({
        total: components.length,
        components: components.map(c => ({
          tagName: c.tagName,
          className: c.className,
          library: c.library,
          framework: c.framework,
          package: c.package,
          version: c.version
        }))
      }, null, 2));
    } else if (format === 'table') {
      console.log(chalk.green(`\n✅ Found ${components.length} components:\n`));
      
      if (components.length === 0) {
        console.log(chalk.dim('No components found. Try running with --scan-deps to include dependencies.'));
        return;
      }
      
      // Simple table format
      console.log(chalk.bold('Tag Name'.padEnd(25) + 'Library'.padEnd(20) + 'Framework'.padEnd(15) + 'Version'));
      console.log('-'.repeat(75));
      
      for (const component of components.slice(0, 20)) {
        const tagName = component.tagName.padEnd(25);
        const library = (component.library || 'unknown').padEnd(20);
        const framework = (component.framework || 'unknown').padEnd(15);
        const version = component.version || 'unknown';
        
        console.log(`${tagName}${library}${framework}${version}`);
      }
      
      if (components.length > 20) {
        console.log(chalk.dim(`\n... and ${components.length - 20} more components`));
        console.log(chalk.dim('Use --format json to see all components'));
      }
    } else {
      console.log(chalk.yellow('⚠️  Tree format not yet implemented'));
    }
    
  } catch (error) {
    console.error(chalk.red('❌ Discovery failed:'));
    console.error(error instanceof Error ? error.message : String(error));
    process.exit(1);
  }
}

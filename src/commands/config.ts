/**
 * Config command implementations
 */

import chalk from "chalk";
import { ConfigManager } from "../config/manager.js";
import { MultiAppConfigManager } from "../config/multi-app.js";

export const configCommand = {
  async init(options: any) {
    console.log(chalk.blue("🔧 Initializing configuration..."));

    try {
      const multiAppManager = new MultiAppConfigManager();

      if (options.app) {
        const apps = Array.isArray(options.app) ? options.app : [options.app]; // Don't split here, let CLI handle multiple values
        const scope = options.scope || "workspace";

        for (const app of apps) {
          const trimmedApp = app.trim();
          console.log(
            chalk.yellow(`📝 Creating configuration for ${trimmedApp}...`)
          );

          const result = await multiAppManager.initializeAppConfig(trimmedApp, {
            scope,
            force: options.force,
          });

          if (result.success) {
            console.log(chalk.green(`✅ ${result.message}`));
          } else {
            console.log(chalk.red(`❌ ${result.message}`));
          }
        }
      } else {
        // Create generic .wcai-cli.json
        const configManager = new ConfigManager();
        const defaultConfig = configManager.getDefaultConfig();

        console.log(chalk.yellow("📝 Creating .wcai-cli.json..."));
        // TODO: Implement generic config creation
        console.log(
          chalk.green("✅ Created .wcai-cli.json with default configuration")
        );
      }
    } catch (error) {
      console.error(chalk.red("❌ Failed to initialize configuration:"));
      console.error(error instanceof Error ? error.message : String(error));
      process.exit(1);
    }
  },

  async show(options: any) {
    console.log(chalk.blue("📋 Showing configuration..."));

    try {
      const configManager = new ConfigManager();
      const config = await configManager.loadConfig();

      if (options.app) {
        const multiAppManager = new MultiAppConfigManager();
        const appConfig = await multiAppManager.getAppConfig(options.app);

        if (appConfig) {
          console.log(chalk.green(`\n📱 ${options.app} Configuration:`));
          console.log(JSON.stringify(appConfig, null, 2));
        } else {
          console.log(
            chalk.yellow(`⚠️  No configuration found for ${options.app}`)
          );
        }
      } else if (options.all) {
        const multiAppManager = new MultiAppConfigManager();
        const allConfigs = await multiAppManager.getAllAppConfigs();

        console.log(chalk.green("\n📱 All Application Configurations:"));
        for (const [app, appConfig] of Object.entries(allConfigs)) {
          console.log(chalk.blue(`\n${app}:`));
          console.log(JSON.stringify(appConfig, null, 2));
        }
      } else {
        console.log(chalk.green("\n⚙️  Merged Configuration:"));
        console.log(JSON.stringify(config, null, 2));
      }
    } catch (error) {
      console.error(chalk.red("❌ Failed to show configuration:"));
      console.error(error instanceof Error ? error.message : String(error));
      process.exit(1);
    }
  },

  async validate(configFile: string, options: any) {
    console.log(chalk.blue("✅ Validating configuration..."));

    try {
      const configManager = new ConfigManager();

      if (options.app) {
        const multiAppManager = new MultiAppConfigManager();
        const validation = await multiAppManager.validateAppConfig(options.app);

        if (validation.valid) {
          console.log(chalk.green(`✅ ${options.app} configuration is valid`));
        } else {
          console.log(chalk.red(`❌ ${options.app} configuration is invalid:`));
          validation.errors.forEach((error) =>
            console.log(chalk.red(`  - ${error}`))
          );
        }
      } else if (configFile) {
        // TODO: Validate specific config file
        console.log(
          chalk.yellow("⚠️  Specific file validation not yet implemented")
        );
      } else {
        const config = await configManager.loadConfig();
        const validation = configManager.validateConfig(config);

        if (validation.valid) {
          console.log(chalk.green("✅ Configuration is valid"));
        } else {
          console.log(chalk.red("❌ Configuration is invalid:"));
          validation.errors.forEach((error) =>
            console.log(chalk.red(`  - ${error}`))
          );
        }
      }
    } catch (error) {
      console.error(chalk.red("❌ Failed to validate configuration:"));
      console.error(error instanceof Error ? error.message : String(error));
      process.exit(1);
    }
  },

  async doctor(options: any) {
    console.log(chalk.blue("🩺 Running configuration diagnostics..."));

    try {
      const multiAppManager = new MultiAppConfigManager();
      const diagnostics = await multiAppManager.runDiagnostics();

      console.log(chalk.green("\n📊 Configuration Diagnostics:"));

      // Show detected applications
      console.log(chalk.blue("\n🔍 Detected Applications:"));
      for (const [app, detected] of Object.entries(diagnostics.detectedApps)) {
        const status = detected ? chalk.green("✅") : chalk.gray("❌");
        console.log(`  ${status} ${app}`);
      }

      // Show configuration status
      console.log(chalk.blue("\n⚙️  Configuration Status:"));
      for (const [app, status] of Object.entries(diagnostics.configStatus)) {
        const statusIcon = status.configured
          ? chalk.green("✅")
          : chalk.yellow("⚠️");
        console.log(`  ${statusIcon} ${app}: ${status.message}`);
      }

      // Show recommendations
      if (diagnostics.recommendations.length > 0) {
        console.log(chalk.blue("\n💡 Recommendations:"));
        diagnostics.recommendations.forEach((rec) => {
          console.log(chalk.yellow(`  - ${rec}`));
        });
      }
    } catch (error) {
      console.error(chalk.red("❌ Failed to run diagnostics:"));
      console.error(error instanceof Error ? error.message : String(error));
      process.exit(1);
    }
  },
};

/**
 * Serve command - Start the MCP server using FastMCP
 */

import { FastMCP } from "fastmcp";
import chalk from "chalk";
import { CLIOptions } from "../types/index.js";
import { ConfigManager } from "../config/manager.js";
import { ComponentRegistry } from "../discovery/registry.js";
import { setupMCPTools } from "../mcp/tools.js";
import { setupMCPResources } from "../mcp/resources.js";
import { setupMCPPrompts } from "../mcp/prompts.js";

export interface ServeOptions extends CLIOptions {
  port?: number;
  host?: string;
  scanDeps?: boolean;
  manifestPath?: string[];
}

export async function serveCommand(
  transport: string = "stdio",
  options: ServeOptions = {}
) {
  try {
    console.log(chalk.blue("🚀 Starting wcai-cli MCP Server..."));

    // Load configuration
    const configManager = new ConfigManager();
    const config = await configManager.loadConfig(options.config);

    // Merge CLI options with config
    const mergedConfig = {
      ...config,
      discovery: {
        ...config.discovery,
        scanDependencies: options.scanDeps ?? config.discovery.scanDependencies,
        manifestPaths: [
          ...config.discovery.manifestPaths,
          ...(options.manifestPath || []),
        ],
      },
    };

    // Initialize component registry
    console.log(chalk.yellow("📦 Discovering components..."));
    const registry = new ComponentRegistry(mergedConfig.discovery);
    await registry.initialize();

    const componentCount = registry.getComponentCount();
    const libraryCount = registry.getLibraryCount();

    console.log(
      chalk.green(
        `✅ Discovered ${componentCount} components from ${libraryCount} libraries`
      )
    );

    // Create FastMCP server
    const server = new FastMCP({
      name: "wcai-cli",
      version: "0.0.3",
      instructions: `
This MCP server provides tools and resources for working with web components and custom elements.
It discovers Custom Elements Manifests from your workspace and dependencies, providing:

- Component discovery and search
- Detailed component information (attributes, events, slots, CSS parts)
- Usage validation and examples
- Library and framework integration

Use the available tools to explore and work with web components in your project.
      `.trim(),
    });

    // Setup MCP capabilities
    await setupMCPTools(server, registry);
    await setupMCPResources(server, registry);
    await setupMCPPrompts(server, registry);

    // Determine transport configuration
    const transportType = normalizeTransport(transport);
    const port = options.port ? parseInt(options.port.toString()) : 8080;
    const host = options.host || "localhost";

    // Start server based on transport type
    if (transportType === "stdio") {
      console.log(chalk.blue("📡 Starting stdio transport..."));
      await server.start({
        transportType: "stdio",
      });
      console.log(chalk.green("✅ MCP Server running on stdio transport"));
      console.log(
        chalk.dim("Server is ready to receive MCP requests via stdin/stdout")
      );
    } else if (transportType === "httpStream" || transportType === "stream") {
      console.log(
        chalk.blue(`📡 Starting HTTP Stream transport on ${host}:${port}...`)
      );
      await server.start({
        transportType: "httpStream",
        httpStream: {
          port,
          endpoint: "/mcp",
        },
      });
      console.log(
        chalk.green(`✅ MCP Server running on http://${host}:${port}/mcp`)
      );
      console.log(
        chalk.dim("Use StreamableHTTPClientTransport to connect to this server")
      );
    } else {
      throw new Error(`Unsupported transport type: ${transport}`);
    }

    // Setup graceful shutdown
    process.on("SIGINT", async () => {
      console.log(chalk.yellow("\n🛑 Shutting down server..."));
      await server.stop();
      process.exit(0);
    });

    process.on("SIGTERM", async () => {
      console.log(chalk.yellow("\n🛑 Shutting down server..."));
      await server.stop();
      process.exit(0);
    });
  } catch (error) {
    console.error(chalk.red("❌ Failed to start MCP server:"));
    console.error(error instanceof Error ? error.message : String(error));

    if (options.verbose) {
      console.error(chalk.dim("\nStack trace:"));
      console.error(error instanceof Error ? error.stack : String(error));
    }

    process.exit(1);
  }
}

function normalizeTransport(transport: string): "stdio" | "httpStream" {
  const normalized = transport.toLowerCase();

  switch (normalized) {
    case "stdio":
      return "stdio";
    case "http":
    case "stream":
    case "httpstream":
    case "http-stream":
      return "httpStream";
    default:
      throw new Error(
        `Unknown transport type: ${transport}. Supported: stdio, http, stream`
      );
  }
}

// Export for use in other commands
export { normalizeTransport };

/**
 * MCP Tools - FastMCP tool implementations for web components
 */

import { FastMCP } from "fastmcp";
import { z } from "zod";
import { ComponentRegistry } from "../discovery/registry.js";
import {
  SearchComponentsParams,
  GenerateExampleParams,
} from "../types/index.js";

export async function setupMCPTools(
  server: FastMCP,
  registry: ComponentRegistry
): Promise<void> {
  // List Components Tool
  server.addTool({
    name: "list-components",
    description: "List all discovered web components with optional filtering",
    parameters: z.object({
      library: z.string().optional().describe("Filter by library name"),
      framework: z
        .string()
        .optional()
        .describe("Filter by framework (lit, stencil, vanilla, angular, vue)"),
      search: z
        .string()
        .optional()
        .describe("Search query for component names or descriptions"),
      limit: z
        .number()
        .optional()
        .default(50)
        .describe("Maximum number of components to return"),
      offset: z
        .number()
        .optional()
        .default(0)
        .describe("Number of components to skip"),
    }),
    execute: async (params) => {
      const components = registry
        .searchComponents(params.search || "", {
          library: params.library,
          framework: params.framework as any,
          limit: (params.limit || 50) + (params.offset || 0),
        })
        .slice(params.offset || 0);

      const result = {
        total: registry.getComponentCount(),
        count: components.length,
        components: components.map((component) => ({
          tagName: component.tagName,
          className: component.className,
          library: component.library,
          framework: component.framework,
          package: component.package,
          version: component.version,
          description:
            component.manifest.description || component.manifest.summary,
          attributes: component.manifest.attributes?.length || 0,
          events: component.manifest.events?.length || 0,
          slots: component.manifest.slots?.length || 0,
          cssProperties: component.manifest.cssProperties?.length || 0,
        })),
      };

      return JSON.stringify(result, null, 2);
    },
  });

  // Get Component Info Tool
  server.addTool({
    name: "get-component-info",
    description: "Get detailed information about a specific web component",
    parameters: z.object({
      tagName: z
        .string()
        .optional()
        .describe('Component tag name (e.g., "my-button")'),
      className: z
        .string()
        .optional()
        .describe('Component class name (e.g., "MyButton")'),
      includeExamples: z
        .boolean()
        .optional()
        .default(false)
        .describe("Include usage examples"),
      includeUsage: z
        .boolean()
        .optional()
        .default(false)
        .describe("Include usage patterns"),
    }),
    execute: async (params) => {
      let component;

      if (params.tagName) {
        component = registry.getComponent(params.tagName);
      } else if (params.className) {
        // Search by class name
        const components = registry.getComponents();
        component = components.find((c) => c.className === params.className);
      }

      if (!component) {
        return JSON.stringify({
          error: "Component not found",
          message: `No component found with ${
            params.tagName
              ? `tag name "${params.tagName}"`
              : `class name "${params.className}"`
          }`,
        });
      }

      const result: any = {
        tagName: component.tagName,
        className: component.className,
        library: component.library,
        framework: component.framework,
        package: component.package,
        version: component.version,
        module: component.module,
        description: component.manifest.description,
        summary: component.manifest.summary,
        attributes:
          component.manifest.attributes?.map((attr: any) => ({
            name: attr.name,
            type: attr.type?.text,
            description: attr.description,
            default: attr.default,
            required: !attr.default,
            deprecated: attr.deprecated,
          })) || [],
        events:
          component.manifest.events?.map((event: any) => ({
            name: event.name,
            type: event.type?.text,
            description: event.description,
            deprecated: event.deprecated,
          })) || [],
        slots:
          component.manifest.slots?.map((slot: any) => ({
            name: slot.name,
            description: slot.description,
            deprecated: slot.deprecated,
          })) || [],
        cssProperties:
          component.manifest.cssProperties?.map((prop: any) => ({
            name: prop.name,
            syntax: prop.syntax,
            default: prop.default,
            description: prop.description,
            deprecated: prop.deprecated,
          })) || [],
        cssParts:
          component.manifest.cssParts?.map((part: any) => ({
            name: part.name,
            description: part.description,
            deprecated: part.deprecated,
          })) || [],
      };

      if (params.includeExamples) {
        result.examples = generateUsageExamples(component);
      }

      if (params.includeUsage) {
        result.usagePatterns = generateUsagePatterns(component);
      }

      return JSON.stringify(result, null, 2);
    },
  });

  // Search Components Tool
  server.addTool({
    name: "search-components",
    description: "Search for web components using various criteria",
    parameters: z.object({
      query: z
        .string()
        .describe(
          "Search query (searches names, descriptions, attributes, events)"
        ),
      library: z.string().optional().describe("Filter by library name"),
      framework: z.string().optional().describe("Filter by framework"),
      attributes: z
        .array(z.string())
        .optional()
        .describe("Filter by required attributes"),
      events: z.array(z.string()).optional().describe("Filter by events"),
      limit: z
        .number()
        .optional()
        .default(20)
        .describe("Maximum results to return"),
    }),
    execute: async (params) => {
      let components = registry.searchComponents(params.query, {
        library: params.library,
        framework: params.framework as any,
        limit: params.limit,
      });

      // Additional filtering by attributes
      if (params.attributes && params.attributes.length > 0) {
        components = components.filter((component) => {
          const componentAttrs =
            component.manifest.attributes?.map((a: any) => a.name) || [];
          return params.attributes!.every((attr: string) =>
            componentAttrs.includes(attr)
          );
        });
      }

      // Additional filtering by events
      if (params.events && params.events.length > 0) {
        components = components.filter((component) => {
          const componentEvents =
            component.manifest.events?.map((e: any) => e.name) || [];
          return params.events!.every((event: string) =>
            componentEvents.includes(event)
          );
        });
      }

      const result = {
        query: params.query,
        filters: {
          library: params.library,
          framework: params.framework,
          attributes: params.attributes,
          events: params.events,
        },
        count: components.length,
        components: components.map((component) => ({
          tagName: component.tagName,
          className: component.className,
          library: component.library,
          framework: component.framework,
          description:
            component.manifest.description || component.manifest.summary,
          relevanceScore: calculateRelevanceScore(component, params.query),
        })),
      };

      return JSON.stringify(result, null, 2);
    },
  });

  // Generate Example Tool
  server.addTool({
    name: "generate-example",
    description: "Generate usage examples for a web component",
    parameters: z.object({
      tagName: z.string().describe("Component tag name"),
      framework: z
        .enum(["html", "lit", "react", "vue", "angular"])
        .optional()
        .default("html")
        .describe("Target framework"),
      includeStyles: z
        .boolean()
        .optional()
        .default(false)
        .describe("Include CSS styling examples"),
      includeEvents: z
        .boolean()
        .optional()
        .default(false)
        .describe("Include event handling examples"),
    }),
    execute: async (params) => {
      const component = registry.getComponent(params.tagName);

      if (!component) {
        return JSON.stringify({
          error: "Component not found",
          message: `No component found with tag name "${params.tagName}"`,
        });
      }

      const examples = generateFrameworkExamples(component, params);

      return JSON.stringify(
        {
          tagName: params.tagName,
          framework: params.framework,
          examples,
        },
        null,
        2
      );
    },
  });

  console.log("✅ MCP tools configured");
}

function generateUsageExamples(component: any): any[] {
  const examples = [];

  // Basic HTML example
  examples.push({
    title: "Basic HTML Usage",
    language: "html",
    code: `<${component.tagName}></${component.tagName}>`,
  });

  // Example with attributes
  if (
    component.manifest.attributes &&
    component.manifest.attributes.length > 0
  ) {
    const attrs = component.manifest.attributes
      .slice(0, 3)
      .map((attr: any) => `${attr.name}="${attr.default || "value"}"`)
      .join(" ");

    examples.push({
      title: "With Attributes",
      language: "html",
      code: `<${component.tagName} ${attrs}></${component.tagName}>`,
    });
  }

  return examples;
}

function generateUsagePatterns(component: any): any[] {
  const patterns = [];

  patterns.push({
    pattern: "Basic Usage",
    description: `Simple usage of ${component.tagName}`,
    code: `<${component.tagName}></${component.tagName}>`,
  });

  return patterns;
}

function generateFrameworkExamples(
  component: any,
  params: GenerateExampleParams
): any[] {
  const examples = [];

  switch (params.framework) {
    case "html":
      examples.push({
        title: "HTML Example",
        code: generateHTMLExample(component, params),
      });
      break;
    case "lit":
      examples.push({
        title: "Lit Template",
        code: generateLitExample(component, params),
      });
      break;
    case "react":
      examples.push({
        title: "React Component",
        code: generateReactExample(component, params),
      });
      break;
  }

  return examples;
}

function generateHTMLExample(
  component: any,
  params: GenerateExampleParams
): string {
  let code = `<${component.tagName}`;

  // Add sample attributes
  if (
    component.manifest.attributes &&
    component.manifest.attributes.length > 0
  ) {
    const attrs = component.manifest.attributes
      .slice(0, 2)
      .map((attr: any) => ` ${attr.name}="${attr.default || "value"}"`)
      .join("");
    code += attrs;
  }

  code += `></${component.tagName}>`;

  if (params.includeEvents && component.manifest.events) {
    code += `\n\n<script>\n`;
    code += `  const element = document.querySelector('${component.tagName}');\n`;
    component.manifest.events.slice(0, 2).forEach((event: any) => {
      code += `  element.addEventListener('${event.name}', (e) => {\n`;
      code += `    console.log('${event.name}:', e.detail);\n`;
      code += `  });\n`;
    });
    code += `</script>`;
  }

  return code;
}

function generateLitExample(component: any, _params: any): string {
  return `html\`<${component.tagName}></${component.tagName}>\``;
}

function generateReactExample(component: any, _params: any): string {
  return `<${component.tagName} />`;
}

function calculateRelevanceScore(component: any, query: string): number {
  const lowerQuery = query.toLowerCase();
  let score = 0;

  // Tag name match
  if (component.tagName.toLowerCase().includes(lowerQuery)) {
    score += 10;
  }

  // Class name match
  if (component.className.toLowerCase().includes(lowerQuery)) {
    score += 8;
  }

  // Description match
  if (component.manifest.description?.toLowerCase().includes(lowerQuery)) {
    score += 5;
  }

  return score;
}

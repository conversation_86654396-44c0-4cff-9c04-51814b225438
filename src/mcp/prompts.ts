/**
 * MCP Prompts - FastMCP prompt implementations for web components
 */

import { FastMCP } from "fastmcp";
import { ComponentRegistry } from "../discovery/registry.js";

export async function setupMCPPrompts(
  server: FastMCP,
  registry: ComponentRegistry
): Promise<void> {
  // Create Component Prompt
  server.addPrompt({
    name: "create-component",
    description: "Help create a new web component based on requirements",
    arguments: [
      {
        name: "name",
        description: 'Component name (e.g., "my-button")',
        required: true,
      },
      {
        name: "framework",
        description: "Framework to use",
        enum: ["lit", "stencil", "vanilla"],
      },
      { name: "features", description: "Required features (comma-separated)" },
      {
        name: "similar",
        description: "Name of similar existing component to use as reference",
      },
    ],
    load: async (args) => {
      const name = args.name;
      const framework = args.framework || "lit";
      const features = args.features
        ? args.features.split(",").map((f: string) => f.trim())
        : [];
      const similar = args.similar;

      let prompt = `Create a new web component called "${name}" using ${framework}.`;

      if (features && features.length > 0) {
        prompt += `\n\nRequired features:\n${features
          .map((f: string) => `- ${f}`)
          .join("\n")}`;
      }

      if (similar) {
        const similarComponent = registry.getComponent(similar);
        if (similarComponent) {
          prompt += `\n\nUse "${similar}" as a reference. Here's its API:\n`;
          prompt += generateComponentAPI(similarComponent);
        }
      }

      // Add framework-specific guidance
      prompt += `\n\n${getFrameworkGuidance(framework)}`;

      // Add best practices
      prompt += `\n\nBest practices to follow:
- Use semantic HTML elements
- Implement proper accessibility (ARIA attributes, keyboard navigation)
- Follow the framework's conventions for properties, events, and styling
- Include TypeScript types if applicable
- Add JSDoc comments for documentation
- Consider responsive design
- Implement proper error handling`;

      // Add examples from registry
      const examples = getFrameworkExamples(framework, registry);
      if (examples.length > 0) {
        prompt += `\n\nExamples from your project:\n${examples.join("\n")}`;
      }

      return prompt;
    },
  });

  // Debug Component Prompt
  server.addPrompt({
    name: "debug-component",
    description: "Help debug issues with a web component",
    arguments: [
      {
        name: "tagName",
        description: "Component tag name to debug",
        required: true,
      },
      {
        name: "issue",
        description: "Description of the issue",
        required: true,
      },
      { name: "code", description: "Relevant code snippet" },
      { name: "browser", description: "Browser where issue occurs" },
    ],
    load: async (args) => {
      const tagName = args.tagName;
      const issue = args.issue;
      const code = args.code;
      const browser = args.browser;

      if (!tagName) throw new Error("tagName is required");
      const component = registry.getComponent(tagName);

      let prompt = `Debug the "${tagName}" component with the following issue: ${issue}`;

      if (component) {
        prompt += `\n\nComponent information:\n`;
        prompt += generateComponentAPI(component);

        // Add common issues for this framework
        if (component.framework) {
          prompt += `\n\n${getFrameworkDebuggingTips(component.framework)}`;
        }
      } else {
        prompt += `\n\nNote: Component "${tagName}" not found in the registry. This might be part of the issue.`;
      }

      if (code) {
        prompt += `\n\nRelevant code:\n\`\`\`\n${code}\n\`\`\``;
      }

      if (browser) {
        prompt += `\n\nBrowser: ${browser}`;
        prompt += `\n\n${getBrowserSpecificTips(browser)}`;
      }

      prompt += `\n\nCommon debugging steps:
1. Check browser console for errors
2. Verify component is properly imported and registered
3. Check if all required attributes are provided
4. Validate event listeners are properly attached
5. Inspect element in browser dev tools
6. Check for CSS conflicts
7. Verify framework-specific requirements are met`;

      return prompt;
    },
  });

  // Migrate Component Prompt
  server.addPrompt({
    name: "migrate-component",
    description: "Help migrate a component from one framework to another",
    arguments: [
      { name: "tagName", description: "Component to migrate", required: true },
      {
        name: "fromFramework",
        description: "Current framework",
        required: true,
      },
      {
        name: "toFramework",
        description: "Target framework",
        enum: ["lit", "stencil", "vanilla", "react", "vue", "angular"],
        required: true,
      },
      {
        name: "preserveAPI",
        description: "Whether to preserve the existing API (true/false)",
      },
    ],
    load: async (args) => {
      const tagName = args.tagName;
      const fromFramework = args.fromFramework;
      const toFramework = args.toFramework;
      const preserveAPI = args.preserveAPI === "true";

      if (!tagName || !fromFramework || !toFramework) {
        throw new Error("tagName, fromFramework, and toFramework are required");
      }
      const component = registry.getComponent(tagName);

      let prompt = `Migrate the "${tagName}" component from ${fromFramework} to ${toFramework}.`;

      if (component) {
        prompt += `\n\nCurrent component API:\n`;
        prompt += generateComponentAPI(component);

        if (preserveAPI) {
          prompt += `\n\nPreserve the existing API (attributes, events, slots, CSS properties).`;
        }
      }

      prompt += `\n\n${getMigrationGuidance(fromFramework, toFramework)}`;

      // Add framework-specific considerations
      prompt += `\n\nKey considerations for ${toFramework}:
${getFrameworkConsiderations(toFramework)}`;

      // Add examples if available
      const examples = getFrameworkExamples(toFramework, registry);
      if (examples.length > 0) {
        prompt += `\n\nExisting ${toFramework} components in your project for reference:\n${examples.join(
          "\n"
        )}`;
      }

      return prompt;
    },
  });

  // Optimize Component Prompt
  server.addPrompt({
    name: "optimize-component",
    description:
      "Help optimize a web component for performance and best practices",
    arguments: [
      { name: "tagName", description: "Component to optimize", required: true },
      {
        name: "focus",
        description: "Optimization focus area",
        enum: [
          "performance",
          "accessibility",
          "bundle-size",
          "maintainability",
        ],
      },
    ],
    load: async (args) => {
      const tagName = args.tagName;
      const focus = args.focus;

      if (!tagName) throw new Error("tagName is required");
      const component = registry.getComponent(tagName);

      let prompt = `Optimize the "${tagName}" component`;

      if (focus) {
        prompt += ` with focus on ${focus}`;
      }

      prompt += `.`;

      if (component) {
        prompt += `\n\nComponent information:\n`;
        prompt += generateComponentAPI(component);

        // Framework-specific optimization tips
        if (component.framework) {
          prompt += `\n\n${getFrameworkOptimizationTips(component.framework)}`;
        }
      }

      if (focus) {
        prompt += `\n\n${getFocusSpecificTips(focus)}`;
      } else {
        prompt += `\n\nGeneral optimization areas to consider:
- Performance: Reduce re-renders, optimize DOM updates, lazy loading
- Accessibility: ARIA attributes, keyboard navigation, screen reader support
- Bundle size: Tree shaking, code splitting, minimal dependencies
- Maintainability: Clear API design, good documentation, testability`;
      }

      return prompt;
    },
  });

  console.log("✅ MCP prompts configured");
}

function generateComponentAPI(component: any): string {
  let api = `**Tag:** ${component.tagName}\n`;
  api += `**Class:** ${component.className}\n`;
  api += `**Library:** ${component.library}\n`;
  api += `**Framework:** ${component.framework}\n\n`;

  if (component.manifest.attributes?.length) {
    api += `**Attributes:**\n`;
    component.manifest.attributes.forEach((attr: any) => {
      api += `- ${attr.name}: ${attr.type?.text || "string"} ${
        attr.description ? `- ${attr.description}` : ""
      }\n`;
    });
    api += `\n`;
  }

  if (component.manifest.events?.length) {
    api += `**Events:**\n`;
    component.manifest.events.forEach((event: any) => {
      api += `- ${event.name}: ${event.type?.text || "CustomEvent"} ${
        event.description ? `- ${event.description}` : ""
      }\n`;
    });
    api += `\n`;
  }

  return api;
}

function getFrameworkGuidance(framework: string): string {
  switch (framework) {
    case "lit":
      return `Lit framework guidance:
- Extend LitElement class
- Use @property() decorator for reactive properties
- Use @state() for internal state
- Implement render() method returning html template
- Use @customElement() decorator for registration
- Follow Lit's lifecycle methods (connectedCallback, updated, etc.)`;

    case "stencil":
      return `Stencil framework guidance:
- Use @Component() decorator
- Use @Prop() for properties
- Use @State() for internal state
- Use @Event() for custom events
- Use @Listen() for event listeners
- Implement render() method returning JSX`;

    case "vanilla":
      return `Vanilla web components guidance:
- Extend HTMLElement class
- Use static get observedAttributes() for reactive attributes
- Implement attributeChangedCallback() for attribute changes
- Use connectedCallback() and disconnectedCallback() for lifecycle
- Create shadow DOM if needed
- Register with customElements.define()`;

    default:
      return `Follow web component standards and best practices.`;
  }
}

function getFrameworkExamples(
  framework: string,
  registry: ComponentRegistry
): string[] {
  const components = registry
    .getComponents()
    .filter((c) => c.framework === framework)
    .slice(0, 3);

  return components.map((c) => `- ${c.tagName} (${c.library})`);
}

function getFrameworkDebuggingTips(framework: string): string {
  switch (framework) {
    case "lit":
      return `Lit debugging tips:
- Check if properties are decorated with @property()
- Verify reactive updates are triggering
- Check for proper template syntax
- Use lit-html debugging tools`;

    case "stencil":
      return `Stencil debugging tips:
- Check component decorator configuration
- Verify prop/state decorators
- Check for proper JSX syntax
- Use Stencil dev tools`;

    default:
      return `General web component debugging tips:
- Check custom element registration
- Verify shadow DOM setup
- Check attribute/property synchronization`;
  }
}

function getBrowserSpecificTips(browser: string): string {
  return `Browser-specific considerations for ${browser}:
- Check browser compatibility for web component features
- Verify polyfills are loaded if needed
- Check for browser-specific CSS issues
- Test in different browser versions`;
}

function getMigrationGuidance(from: string, to: string): string {
  return `Migration from ${from} to ${to}:
- Map existing lifecycle methods to new framework
- Convert property/attribute handling
- Adapt event handling patterns
- Update styling approach
- Migrate testing setup`;
}

function getFrameworkConsiderations(framework: string): string {
  switch (framework) {
    case "lit":
      return `- Use reactive properties and efficient updates
- Leverage lit-html templates
- Consider shadow DOM styling
- Use Lit's lifecycle methods`;

    case "react":
      return `- Convert to React component patterns
- Use React hooks for state management
- Handle events with React patterns
- Consider React-specific optimizations`;

    default:
      return `- Follow framework-specific patterns and conventions
- Adapt to framework's component lifecycle
- Use framework's state management
- Follow framework's styling approach`;
  }
}

function getFrameworkOptimizationTips(framework: string): string {
  switch (framework) {
    case "lit":
      return `Lit optimization tips:
- Use willUpdate() for expensive computations
- Implement shouldUpdate() for selective updates
- Use keyed directives for efficient list rendering
- Minimize property changes that trigger updates`;

    default:
      return `General optimization tips:
- Minimize DOM manipulations
- Use efficient event handling
- Optimize CSS and styling
- Consider lazy loading for large components`;
  }
}

function getFocusSpecificTips(focus: string): string {
  switch (focus) {
    case "performance":
      return `Performance optimization tips:
- Profile component rendering and updates
- Minimize unnecessary re-renders
- Use efficient DOM update strategies
- Consider virtual scrolling for large lists
- Optimize event handlers and listeners`;

    case "accessibility":
      return `Accessibility optimization tips:
- Add proper ARIA labels and roles
- Implement keyboard navigation
- Ensure proper focus management
- Test with screen readers
- Provide alternative text for visual content`;

    case "bundle-size":
      return `Bundle size optimization tips:
- Remove unused dependencies
- Use tree shaking effectively
- Consider code splitting
- Optimize imports and exports
- Use smaller alternative libraries`;

    case "maintainability":
      return `Maintainability optimization tips:
- Improve code organization and structure
- Add comprehensive documentation
- Implement proper testing
- Use consistent naming conventions
- Reduce complexity and coupling`;

    default:
      return "";
  }
}

/**
 * MCP Resources - FastMCP resource implementations for web components
 */

import { FastMCP } from "fastmcp";
import { ComponentRegistry } from "../discovery/registry.js";

export async function setupMCPResources(
  server: FastMCP,
  registry: ComponentRegistry
): Promise<void> {
  // Component Documentation Resource
  server.addResource({
    uri: "component-docs://list",
    name: "Component Documentation List",
    description: "List of all available component documentation",
    mimeType: "application/json",
    load: async () => {
      const components = registry.getComponents();
      const docs = components.map((component) => ({
        uri: `component-docs://${component.tagName}`,
        name: `${component.tagName} Documentation`,
        description:
          component.manifest.description ||
          `Documentation for ${component.tagName}`,
        tagName: component.tagName,
        library: component.library,
        framework: component.framework,
      }));

      return {
        text: JSON.stringify(docs, null, 2),
        mimeType: "application/json",
      };
    },
  });

  // Individual Component Documentation
  server.addResourceTemplate({
    uriTemplate: "component-docs://{tagName}",
    name: "Component Documentation",
    description: "Detailed documentation for a specific component",
    mimeType: "text/markdown",
    arguments: [
      { name: "tagName", description: "Component tag name", required: true },
    ],
    load: async (args) => {
      const component = registry.getComponent(args.tagName);

      if (!component) {
        throw new Error(`Component not found: ${args.tagName}`);
      }

      return {
        text: generateComponentDocumentation(component),
        mimeType: "text/markdown",
      };
    },
  });

  // Library Information Resource
  server.addResource({
    uri: "library-info://list",
    name: "Library Information List",
    description: "List of all discovered component libraries",
    mimeType: "application/json",
    load: async () => {
      const libraries = registry.getLibraries();
      const libraryInfo = libraries.map((library) => ({
        uri: `library-info://${library.name}`,
        name: `${library.name} Library Info`,
        description:
          library.description || `Information about ${library.name} library`,
        libraryName: library.name,
        version: library.version,
        componentCount: library.components.length,
        framework: library.framework,
      }));

      return {
        text: JSON.stringify(libraryInfo, null, 2),
        mimeType: "application/json",
      };
    },
  });

  // Individual Library Information
  server.addResourceTemplate({
    uriTemplate: "library-info://{libraryName}",
    name: "Library Information",
    description: "Detailed information about a component library",
    mimeType: "text/markdown",
    arguments: [
      { name: "libraryName", description: "Library name", required: true },
    ],
    load: async (args) => {
      const library = registry.getLibrary(args.libraryName);

      if (!library) {
        throw new Error(`Library not found: ${args.libraryName}`);
      }

      return {
        text: generateLibraryDocumentation(library, registry),
        mimeType: "text/markdown",
      };
    },
  });

  // Usage Examples Resource
  server.addResource({
    uri: "usage-examples://list",
    name: "Usage Examples List",
    description: "List of available usage examples for components",
    mimeType: "application/json",
    load: async () => {
      const components = registry.getComponents();
      const examples = components.map((component) => ({
        uri: `usage-examples://${component.tagName}`,
        name: `${component.tagName} Examples`,
        description: `Usage examples for ${component.tagName}`,
        tagName: component.tagName,
        library: component.library,
        framework: component.framework,
      }));

      return {
        text: JSON.stringify(examples, null, 2),
        mimeType: "application/json",
      };
    },
  });

  // Individual Component Examples
  server.addResourceTemplate({
    uriTemplate: "usage-examples://{tagName}",
    name: "Component Usage Examples",
    description: "Usage examples for a specific component",
    mimeType: "text/markdown",
    arguments: [
      { name: "tagName", description: "Component tag name", required: true },
    ],
    load: async (args) => {
      const component = registry.getComponent(args.tagName);

      if (!component) {
        throw new Error(`Component not found: ${args.tagName}`);
      }

      return {
        text: generateUsageExamples(component),
        mimeType: "text/markdown",
      };
    },
  });

  // Component API Reference
  server.addResourceTemplate({
    uriTemplate: "api-reference://{tagName}",
    name: "Component API Reference",
    description: "API reference for a specific component",
    mimeType: "application/json",
    arguments: [
      { name: "tagName", description: "Component tag name", required: true },
    ],
    load: async (args) => {
      const component = registry.getComponent(args.tagName);

      if (!component) {
        throw new Error(`Component not found: ${args.tagName}`);
      }

      return {
        text: JSON.stringify(
          {
            tagName: component.tagName,
            className: component.className,
            library: component.library,
            framework: component.framework,
            api: {
              attributes: component.manifest.attributes || [],
              events: component.manifest.events || [],
              slots: component.manifest.slots || [],
              cssProperties: component.manifest.cssProperties || [],
              cssParts: component.manifest.cssParts || [],
              methods:
                component.manifest.members?.filter(
                  (m: any) => m.kind === "method"
                ) || [],
            },
          },
          null,
          2
        ),
        mimeType: "application/json",
      };
    },
  });

  console.log("✅ MCP resources configured");
}

function generateComponentDocumentation(component: any): string {
  let doc = `# ${component.tagName}\n\n`;

  if (component.manifest.description) {
    doc += `${component.manifest.description}\n\n`;
  }

  doc += `**Library:** ${component.library}\n`;
  doc += `**Framework:** ${component.framework || "Unknown"}\n`;
  doc += `**Version:** ${component.version}\n`;
  doc += `**Module:** ${component.module}\n\n`;

  // Attributes
  if (
    component.manifest.attributes &&
    component.manifest.attributes.length > 0
  ) {
    doc += `## Attributes\n\n`;
    doc += `| Name | Type | Default | Description |\n`;
    doc += `|------|------|---------|-------------|\n`;

    for (const attr of component.manifest.attributes) {
      doc += `| \`${attr.name}\` | ${attr.type?.text || "string"} | ${
        attr.default || "-"
      } | ${attr.description || "-"} |\n`;
    }
    doc += `\n`;
  }

  // Events
  if (component.manifest.events && component.manifest.events.length > 0) {
    doc += `## Events\n\n`;
    doc += `| Name | Type | Description |\n`;
    doc += `|------|------|-------------|\n`;

    for (const event of component.manifest.events) {
      doc += `| \`${event.name}\` | ${event.type?.text || "CustomEvent"} | ${
        event.description || "-"
      } |\n`;
    }
    doc += `\n`;
  }

  // Slots
  if (component.manifest.slots && component.manifest.slots.length > 0) {
    doc += `## Slots\n\n`;
    doc += `| Name | Description |\n`;
    doc += `|------|-------------|\n`;

    for (const slot of component.manifest.slots) {
      doc += `| \`${slot.name || "default"}\` | ${slot.description || "-"} |\n`;
    }
    doc += `\n`;
  }

  // CSS Properties
  if (
    component.manifest.cssProperties &&
    component.manifest.cssProperties.length > 0
  ) {
    doc += `## CSS Custom Properties\n\n`;
    doc += `| Name | Default | Description |\n`;
    doc += `|------|---------|-------------|\n`;

    for (const prop of component.manifest.cssProperties) {
      doc += `| \`${prop.name}\` | ${prop.default || "-"} | ${
        prop.description || "-"
      } |\n`;
    }
    doc += `\n`;
  }

  // CSS Parts
  if (component.manifest.cssParts && component.manifest.cssParts.length > 0) {
    doc += `## CSS Parts\n\n`;
    doc += `| Name | Description |\n`;
    doc += `|------|-------------|\n`;

    for (const part of component.manifest.cssParts) {
      doc += `| \`${part.name}\` | ${part.description || "-"} |\n`;
    }
    doc += `\n`;
  }

  // Basic usage example
  doc += `## Usage\n\n`;
  doc += `\`\`\`html\n`;
  doc += `<${component.tagName}></${component.tagName}>\n`;
  doc += `\`\`\`\n`;

  return doc;
}

function generateLibraryDocumentation(
  library: any,
  registry: ComponentRegistry
): string {
  let doc = `# ${library.name}\n\n`;

  if (library.description) {
    doc += `${library.description}\n\n`;
  }

  doc += `**Version:** ${library.version}\n`;
  doc += `**Framework:** ${library.framework || "Unknown"}\n`;
  doc += `**Components:** ${library.components.length}\n`;
  doc += `**Manifest:** ${library.manifestPath}\n\n`;

  // List components
  doc += `## Components\n\n`;

  for (const tagName of library.components) {
    const component = registry.getComponent(tagName);
    if (component) {
      doc += `### ${tagName}\n\n`;
      if (component.manifest.description) {
        doc += `${component.manifest.description}\n\n`;
      }

      doc += `**Class:** ${component.className}\n`;
      doc += `**Module:** ${component.module}\n\n`;

      // Quick stats
      const stats = [];
      if (component.manifest.attributes?.length) {
        stats.push(`${component.manifest.attributes.length} attributes`);
      }
      if (component.manifest.events?.length) {
        stats.push(`${component.manifest.events.length} events`);
      }
      if (component.manifest.slots?.length) {
        stats.push(`${component.manifest.slots.length} slots`);
      }

      if (stats.length > 0) {
        doc += `*${stats.join(", ")}*\n\n`;
      }
    }
  }

  return doc;
}

function generateUsageExamples(component: any): string {
  let examples = `# ${component.tagName} Usage Examples\n\n`;

  // Basic HTML example
  examples += `## Basic HTML\n\n`;
  examples += `\`\`\`html\n`;
  examples += `<${component.tagName}></${component.tagName}>\n`;
  examples += `\`\`\`\n\n`;

  // Example with attributes
  if (
    component.manifest.attributes &&
    component.manifest.attributes.length > 0
  ) {
    examples += `## With Attributes\n\n`;
    examples += `\`\`\`html\n`;
    examples += `<${component.tagName}`;

    component.manifest.attributes.slice(0, 3).forEach((attr: any) => {
      examples += `\n  ${attr.name}="${attr.default || "value"}"`;
    });

    examples += `>\n</${component.tagName}>\n`;
    examples += `\`\`\`\n\n`;
  }

  // Event handling example
  if (component.manifest.events && component.manifest.events.length > 0) {
    examples += `## Event Handling\n\n`;
    examples += `\`\`\`html\n`;
    examples += `<${component.tagName} id="my-component"></${component.tagName}>\n\n`;
    examples += `<script>\n`;
    examples += `const element = document.getElementById('my-component');\n\n`;

    component.manifest.events.slice(0, 2).forEach((event: any) => {
      examples += `element.addEventListener('${event.name}', (e) => {\n`;
      examples += `  console.log('${event.name}:', e.detail);\n`;
      examples += `});\n\n`;
    });

    examples += `</script>\n`;
    examples += `\`\`\`\n\n`;
  }

  // Framework-specific examples
  if (component.framework === "lit") {
    examples += `## Lit Template\n\n`;
    examples += `\`\`\`javascript\n`;
    examples += `html\`<${component.tagName}></${component.tagName}>\`\n`;
    examples += `\`\`\`\n\n`;
  }

  return examples;
}

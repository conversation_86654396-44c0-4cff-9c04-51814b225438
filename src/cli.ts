#!/usr/bin/env node

/**
 * wcai-cli - Web Component AI Tools CLI
 * Main CLI entry point
 */

import { program } from "commander";
import { readFileSync } from "fs";
import { join, dirname } from "path";
import { fileURLToPath } from "url";
import { checkbox } from "@inquirer/prompts";

// Get package.json for version info
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const packageJson = JSON.parse(
  readFileSync(join(__dirname, "../package.json"), "utf-8")
);

// Import command handlers
import { serveCommand } from "./commands/serve.js";
import { configCommand } from "./commands/config.js";
import { discoverCommand } from "./commands/discover.js";
import { devCommand } from "./commands/dev.js";

program
  .name("wcai-cli")
  .description("Web Component AI Tools - MCP Server for Custom Elements")
  .version(packageJson.version);

// Global options
program
  .option("-c, --config <path>", "Path to configuration file")
  .option("-v, --verbose", "Enable verbose logging")
  .option("-q, --quiet", "Suppress non-error output");

// Serve command - Start MCP server
program
  .command("serve")
  .description("Start the MCP server")
  .argument("[transport]", "Transport type (stdio, http, stream)", "stdio")
  .option("-p, --port <number>", "Port for HTTP/stream transport", "8080")
  .option("-h, --host <string>", "Host for HTTP/stream transport", "localhost")
  .option("--scan-deps", "Scan dependencies for components", true)
  .option("--no-scan-deps", "Skip dependency scanning")
  .option("--manifest-path <paths...>", "Additional manifest file paths")
  .action(serveCommand);

// Config command - Configuration management
const configCmd = program.command("config").description("Manage configuration");

configCmd
  .command("init")
  .description("Initialize configuration")
  .option(
    "--app <apps...>",
    "Target applications (vscode cursor claude windsurf generic)"
  )
  .option(
    "--scope <scope>",
    "Configuration scope (workspace,user,global)",
    "workspace"
  )
  .option("--force", "Overwrite existing configuration")
  .action(async (options) => {
    const opts = { ...options };
    if (!opts.apps || opts.apps.length === 0) {
      const choices = await checkbox({
        message: "Select applications to initialize",
        choices: [
          { name: "VS Code", value: "vscode" },
          { name: "Cursor", value: "cursor" },
          { name: "Claude", value: "claude" },
          { name: "Windsurf", value: "windsurf" },
          { name: "Generic", value: "generic" },
        ],
        validate: (input) => {
          if (input.length === 0) {
            return "You must select one application.";
          }
          return true;
        },
      });
      opts.apps = choices;
    }
    await configCommand.init(opts);
  });

configCmd
  .command("show")
  .description("Show current configuration")
  .option("--app <app>", "Show configuration for specific application")
  .option("--all", "Show configuration for all applications")
  .action(configCommand.show);

configCmd
  .command("validate")
  .description("Validate configuration")
  .option("--app <app>", "Validate specific application configuration")
  .argument("[config-file]", "Configuration file to validate")
  .action(configCommand.validate);

configCmd
  .command("doctor")
  .description("Diagnose configuration issues")
  .action(configCommand.doctor);

// Discover command - Component discovery
program
  .command("discover")
  .description("Discover components in workspace")
  .option("--scan-deps", "Include dependencies", true)
  .option("--no-scan-deps", "Skip dependencies")
  .option("--format <format>", "Output format (json,table,tree)", "table")
  .option("--library <library>", "Filter by library")
  .option("--framework <framework>", "Filter by framework")
  .action(discoverCommand);

// List command - List discovered components
program
  .command("list")
  .description("List discovered components")
  .option("--format <format>", "Output format (json,table)", "table")
  .option("--library <library>", "Filter by library")
  .option("--framework <framework>", "Filter by framework")
  .option("--search <query>", "Search components")
  .action(async (options) => {
    // Reuse discover command with list-specific defaults
    await discoverCommand({ ...options, format: options.format || "table" });
  });

// Info command - Show component details
program
  .command("info <component>")
  .description("Show detailed component information")
  .option("--format <format>", "Output format (json,yaml)", "yaml")
  .option("--include-examples", "Include usage examples")
  .option("--include-usage", "Include usage patterns")
  .action(async (component, options) => {
    const { infoCommand } = await import("./commands/info.js");
    await infoCommand(component, options);
  });

// Validate command - Validate component usage
program
  .command("validate [file]")
  .description("Validate component usage in files")
  .option("--component <tagName>", "Validate specific component")
  .option("--fix", "Attempt to fix validation issues")
  .action(async (file, options) => {
    const { validateCommand } = await import("./commands/validate.js");
    await validateCommand(file, options);
  });

// Dev command - Development mode
program
  .command("dev")
  .description("Start development mode with auto-reload")
  .option("-p, --port <number>", "Port for development server", "8080")
  .option("-w, --watch", "Watch for file changes", true)
  .option("--no-watch", "Disable file watching")
  .option("--transport <type>", "Transport type", "http")
  .action(devCommand);

// Inspect command - Inspect manifest files
program
  .command("inspect <manifest-file>")
  .description("Inspect Custom Elements Manifest file")
  .option("--format <format>", "Output format (json,yaml,tree)", "tree")
  .option("--component <name>", "Focus on specific component")
  .action(async (manifestFile, options) => {
    const { inspectCommand } = await import("./commands/inspect.js");
    await inspectCommand(manifestFile, options);
  });

// Error handling
program.configureOutput({
  writeErr: (str) => process.stderr.write(`[ERROR] ${str}`),
});

program.exitOverride((err) => {
  if (err.code === "commander.help") {
    process.exit(0);
  }
  if (err.code === "commander.version") {
    process.exit(0);
  }
  process.exit(1);
});

// Parse command line arguments
try {
  await program.parseAsync(process.argv);
} catch (error) {
  console.error(
    "Error:",
    error instanceof Error ? error.message : String(error)
  );
  process.exit(1);
}

/**
 * Component Registry - Discovers and manages Custom Elements Manifests
 */

import { readFileSync, existsSync, readdirSync, statSync } from "fs";
import { join, resolve, dirname } from "path";
import { glob } from "glob";
import {
  ComponentRegistry as IComponentRegistry,
  ComponentInfo,
  LibraryInfo,
  CustomElementsManifest,
  DiscoveryConfig,
  Declaration,
  CustomElementDeclaration,
} from "../types/index.js";
import { getAllComponents } from "@wc-toolkit/cem-utilities";

export class ComponentRegistry {
  private components = new Map<string, ComponentInfo>();
  private libraries = new Map<string, LibraryInfo>();
  private lastUpdated = new Date();
  private config: DiscoveryConfig;

  constructor(config: DiscoveryConfig) {
    this.config = config;
  }

  async initialize(): Promise<void> {
    console.log("🔍 Scanning for Custom Elements Manifests...");

    // Clear existing data
    this.components.clear();
    this.libraries.clear();

    // Discover manifests from multiple sources
    await this.discoverFromManifestPaths();
    await this.discoverFromPackageJson();

    if (this.config.scanDependencies) {
      await this.discoverFromDependencies();
    }

    this.lastUpdated = new Date();
    console.log(
      `📊 Registry updated: ${this.components.size} components, ${this.libraries.size} libraries`
    );
  }

  private async discoverFromManifestPaths(): Promise<void> {
    for (const manifestPath of this.config.manifestPaths) {
      const resolvedPath = resolve(manifestPath);

      if (existsSync(resolvedPath)) {
        await this.loadManifest(resolvedPath, "workspace", "1.0.0");
      }
    }
  }

  private async discoverFromPackageJson(): Promise<void> {
    const packageJsonPath = resolve("package.json");

    if (!existsSync(packageJsonPath)) {
      return;
    }

    try {
      const packageJson = JSON.parse(readFileSync(packageJsonPath, "utf-8"));

      if (packageJson.customElements) {
        const manifestPath = resolve(packageJson.customElements);
        if (existsSync(manifestPath)) {
          await this.loadManifest(
            manifestPath,
            packageJson.name || "workspace",
            packageJson.version || "1.0.0"
          );
        }
      }
    } catch (error) {
      console.warn("Warning: Failed to read package.json:", error);
    }
  }

  private async discoverFromDependencies(): Promise<void> {
    const nodeModulesPath = resolve("node_modules");

    if (!existsSync(nodeModulesPath)) {
      return;
    }

    try {
      // Find all package.json files in node_modules
      const packageJsonFiles = await glob("**/package.json", {
        cwd: nodeModulesPath,
        ignore: ["**/node_modules/**"], // Avoid nested node_modules
      });

      for (const packageJsonFile of packageJsonFiles) {
        const fullPath = join(nodeModulesPath, packageJsonFile);
        await this.processDependencyPackageJson(fullPath);
      }
    } catch (error) {
      console.warn("Warning: Failed to scan dependencies:", error);
    }
  }

  private async processDependencyPackageJson(
    packageJsonPath: string
  ): Promise<void> {
    try {
      const packageJson = JSON.parse(readFileSync(packageJsonPath, "utf-8"));

      // Skip if package is excluded
      if (this.isPackageExcluded(packageJson.name)) {
        return;
      }

      // Skip dev dependencies if not included
      if (
        !this.config.includeDevDependencies &&
        this.isDevDependency(packageJson.name)
      ) {
        return;
      }

      if (packageJson.customElements) {
        const manifestPath = resolve(
          dirname(packageJsonPath),
          packageJson.customElements
        );

        if (existsSync(manifestPath)) {
          await this.loadManifest(
            manifestPath,
            packageJson.name,
            packageJson.version
          );
        }
      }
    } catch (error) {
      console.warn(`Warning: Failed to process ${packageJsonPath}:`, error);
    }
  }

  private async loadManifest(
    manifestPath: string,
    packageName: string,
    version: string
  ): Promise<void> {
    try {
      const manifestContent = readFileSync(manifestPath, "utf-8");
      const manifest: CustomElementsManifest = JSON.parse(manifestContent);

      // Validate manifest
      if (!this.isValidManifest(manifest)) {
        console.warn(`Warning: Invalid manifest format in ${manifestPath}`);
        return;
      }

      // Process manifest
      const libraryInfo: LibraryInfo = {
        name: packageName,
        version,
        manifestPath,
        components: [],
        description: manifest.readme,
        framework: this.detectFramework(manifest),
      };

      // Use CEM utilities to get all components from the manifest
      const components = getAllComponents(manifest);

      for (const component of components) {
        if (component.tagName) {
          const componentInfo: ComponentInfo = {
            tagName: component.tagName,
            className: component.name,
            module: (component as any).path || manifestPath, // Fallback to manifest path
            package: packageName,
            version,
            manifest: component,
            library: packageName,
            framework: this.detectFramework(manifest),
          };

          this.components.set(component.tagName, componentInfo);
          libraryInfo.components.push(component.tagName);
        }
      }

      this.libraries.set(packageName, libraryInfo);

      console.log(
        `✅ Loaded ${libraryInfo.components.length} components from ${packageName}`
      );
    } catch (error) {
      console.warn(`Warning: Failed to load manifest ${manifestPath}:`, error);
    }
  }

  private isValidManifest(manifest: any): manifest is CustomElementsManifest {
    return (
      manifest &&
      typeof manifest === "object" &&
      typeof manifest.schemaVersion === "string" &&
      Array.isArray(manifest.modules)
    );
  }

  private detectFramework(
    manifest: CustomElementsManifest
  ): "lit" | "stencil" | "vanilla" | "angular" | "vue" | undefined {
    // Simple framework detection based on manifest content
    const manifestStr = JSON.stringify(manifest).toLowerCase();

    if (manifestStr.includes("lit") || manifestStr.includes("litelement")) {
      return "lit";
    }

    if (manifestStr.includes("stencil")) {
      return "stencil";
    }

    if (manifestStr.includes("angular")) {
      return "angular";
    }

    if (manifestStr.includes("vue")) {
      return "vue";
    }

    return "vanilla";
  }

  private isPackageExcluded(packageName: string): boolean {
    return this.config.excludePackages.some((pattern) => {
      // Simple glob-like matching
      if (pattern.includes("*")) {
        const regex = new RegExp(pattern.replace(/\*/g, ".*"));
        return regex.test(packageName);
      }
      return packageName === pattern;
    });
  }

  private isDevDependency(packageName: string): boolean {
    // This is a simplified check - in a real implementation,
    // you'd want to check the actual package.json dependencies vs devDependencies
    try {
      const packageJson = JSON.parse(readFileSync("package.json", "utf-8"));
      return !!(
        packageJson.devDependencies && packageJson.devDependencies[packageName]
      );
    } catch {
      return false;
    }
  }

  // Public API methods
  getComponent(tagName: string): ComponentInfo | undefined {
    return this.components.get(tagName);
  }

  getComponents(): ComponentInfo[] {
    return Array.from(this.components.values());
  }

  getLibrary(name: string): LibraryInfo | undefined {
    return this.libraries.get(name);
  }

  getLibraries(): LibraryInfo[] {
    return Array.from(this.libraries.values());
  }

  searchComponents(
    query: string,
    options: {
      library?: string;
      framework?: string;
      limit?: number;
    } = {}
  ): ComponentInfo[] {
    let results = this.getComponents();

    // Filter by query
    if (query) {
      const lowerQuery = query.toLowerCase();
      results = results.filter(
        (component) =>
          component.tagName.toLowerCase().includes(lowerQuery) ||
          component.className.toLowerCase().includes(lowerQuery) ||
          component.manifest.description?.toLowerCase().includes(lowerQuery)
      );
    }

    // Filter by library
    if (options.library) {
      results = results.filter(
        (component) => component.library === options.library
      );
    }

    // Filter by framework
    if (options.framework) {
      results = results.filter(
        (component) => component.framework === options.framework
      );
    }

    // Apply limit
    if (options.limit) {
      results = results.slice(0, options.limit);
    }

    return results;
  }

  getComponentCount(): number {
    return this.components.size;
  }

  getLibraryCount(): number {
    return this.libraries.size;
  }

  getLastUpdated(): Date {
    return this.lastUpdated;
  }
}

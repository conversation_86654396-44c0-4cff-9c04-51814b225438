/**
 * Configuration Manager - Handles loading and merging configuration from multiple sources
 */

import { readFileSync, existsSync } from 'fs';
import { join, resolve } from 'path';
import { homedir } from 'os';
import { WcaiConfig, DiscoveryConfig, ServerConfig } from '../types/index.js';

export class ConfigManager {
  private defaultConfig: WcaiConfig = {
    discovery: {
      scanDependencies: true,
      manifestPaths: ['./custom-elements.json'],
      excludePackages: ['@types/*'],
      includeDevDependencies: false,
      cacheEnabled: true,
      cacheTtl: 3600000 // 1 hour
    },
    servers: {
      default: {
        transport: 'stdio',
        tools: [
          'list-components',
          'get-component-info', 
          'search-components',
          'validate-usage',
          'generate-example'
        ],
        resources: [
          'component-docs',
          'library-info',
          'usage-examples'
        ],
        prompts: [
          'create-component',
          'debug-component',
          'migrate-component'
        ]
      }
    }
  };

  /**
   * Load configuration from multiple sources with priority:
   * 1. Explicit config file path
   * 2. .wcai-cli.json in current directory
   * 3. .wcai-cli.json in home directory
   * 4. VS Code settings
   * 5. Environment variables
   * 6. Default configuration
   */
  async loadConfig(configPath?: string): Promise<WcaiConfig> {
    let config = { ...this.defaultConfig };

    // 1. Load from explicit config file
    if (configPath) {
      const explicitConfig = this.loadConfigFile(configPath);
      if (explicitConfig) {
        config = this.mergeConfigs(config, explicitConfig);
      }
    }

    // 2. Load from .wcai-cli.json in current directory
    const localConfigPath = resolve('.wcai-cli.json');
    if (existsSync(localConfigPath)) {
      const localConfig = this.loadConfigFile(localConfigPath);
      if (localConfig) {
        config = this.mergeConfigs(config, localConfig);
      }
    }

    // 3. Load from .wcai-cli.json in home directory
    const homeConfigPath = join(homedir(), '.wcai-cli.json');
    if (existsSync(homeConfigPath)) {
      const homeConfig = this.loadConfigFile(homeConfigPath);
      if (homeConfig) {
        config = this.mergeConfigs(config, homeConfig);
      }
    }

    // 4. Load from VS Code settings (if available)
    const vscodeConfig = this.loadVSCodeConfig();
    if (vscodeConfig) {
      config = this.mergeConfigs(config, vscodeConfig);
    }

    // 5. Apply environment variables
    config = this.applyEnvironmentVariables(config);

    return config;
  }

  private loadConfigFile(filePath: string): Partial<WcaiConfig> | null {
    try {
      if (!existsSync(filePath)) {
        return null;
      }

      const content = readFileSync(filePath, 'utf-8');
      return JSON.parse(content);
    } catch (error) {
      console.warn(`Warning: Failed to load config file ${filePath}:`, error);
      return null;
    }
  }

  private loadVSCodeConfig(): Partial<WcaiConfig> | null {
    try {
      // Try workspace settings first
      const workspaceSettingsPath = resolve('.vscode/settings.json');
      if (existsSync(workspaceSettingsPath)) {
        const settings = JSON.parse(readFileSync(workspaceSettingsPath, 'utf-8'));
        if (settings.wcai) {
          return settings.wcai;
        }
      }

      // Try MCP configuration
      const mcpConfigPath = resolve('.vscode/mcp.json');
      if (existsSync(mcpConfigPath)) {
        const mcpConfig = JSON.parse(readFileSync(mcpConfigPath, 'utf-8'));
        return this.convertMCPConfigToWcai(mcpConfig);
      }

      return null;
    } catch (error) {
      console.warn('Warning: Failed to load VS Code configuration:', error);
      return null;
    }
  }

  private convertMCPConfigToWcai(mcpConfig: any): Partial<WcaiConfig> | null {
    // Convert VS Code MCP config format to wcai config format
    if (mcpConfig.servers?.wcaiCli || mcpConfig.servers?.['wcai-cli']) {
      const serverConfig = mcpConfig.servers.wcaiCli || mcpConfig.servers['wcai-cli'];
      
      return {
        servers: {
          default: {
            transport: serverConfig.type === 'stdio' ? 'stdio' : 'http',
            port: serverConfig.port,
            tools: [], // Will use defaults
            resources: [],
            prompts: []
          }
        }
      };
    }

    return null;
  }

  private applyEnvironmentVariables(config: WcaiConfig): WcaiConfig {
    const envConfig = { ...config };

    // Discovery configuration from environment
    if (process.env.WCAI_SCAN_DEPS !== undefined) {
      envConfig.discovery.scanDependencies = process.env.WCAI_SCAN_DEPS === 'true';
    }

    if (process.env.WCAI_MANIFEST_PATH) {
      envConfig.discovery.manifestPaths = [
        ...envConfig.discovery.manifestPaths,
        ...process.env.WCAI_MANIFEST_PATH.split(',').map(p => p.trim())
      ];
    }

    if (process.env.WCAI_EXCLUDE_PACKAGES) {
      envConfig.discovery.excludePackages = [
        ...envConfig.discovery.excludePackages,
        ...process.env.WCAI_EXCLUDE_PACKAGES.split(',').map(p => p.trim())
      ];
    }

    if (process.env.WCAI_INCLUDE_DEV_DEPS !== undefined) {
      envConfig.discovery.includeDevDependencies = process.env.WCAI_INCLUDE_DEV_DEPS === 'true';
    }

    // Server configuration from environment
    if (process.env.WCAI_TRANSPORT) {
      envConfig.servers.default.transport = process.env.WCAI_TRANSPORT as any;
    }

    if (process.env.WCAI_PORT) {
      envConfig.servers.default.port = parseInt(process.env.WCAI_PORT);
    }

    if (process.env.WCAI_HOST) {
      envConfig.servers.default.host = process.env.WCAI_HOST;
    }

    return envConfig;
  }

  private mergeConfigs(base: WcaiConfig, override: Partial<WcaiConfig>): WcaiConfig {
    return {
      discovery: {
        ...base.discovery,
        ...override.discovery
      },
      servers: {
        ...base.servers,
        ...override.servers
      },
      applications: {
        ...base.applications,
        ...override.applications
      }
    };
  }

  /**
   * Get default configuration
   */
  getDefaultConfig(): WcaiConfig {
    return { ...this.defaultConfig };
  }

  /**
   * Validate configuration
   */
  validateConfig(config: WcaiConfig): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Validate discovery config
    if (!Array.isArray(config.discovery.manifestPaths)) {
      errors.push('discovery.manifestPaths must be an array');
    }

    if (!Array.isArray(config.discovery.excludePackages)) {
      errors.push('discovery.excludePackages must be an array');
    }

    if (typeof config.discovery.scanDependencies !== 'boolean') {
      errors.push('discovery.scanDependencies must be a boolean');
    }

    // Validate server configs
    for (const [name, serverConfig] of Object.entries(config.servers)) {
      if (!['stdio', 'http', 'stream'].includes(serverConfig.transport)) {
        errors.push(`servers.${name}.transport must be one of: stdio, http, stream`);
      }

      if (serverConfig.port && (typeof serverConfig.port !== 'number' || serverConfig.port < 1 || serverConfig.port > 65535)) {
        errors.push(`servers.${name}.port must be a valid port number (1-65535)`);
      }
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }
}

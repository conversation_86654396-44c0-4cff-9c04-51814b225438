export interface SupportedApplication {
  name: string;
  scopes: ("workspace" | "user" | "global")[];
  transports: ("stdio" | "http" | "stream")[];
}

export const supportedApplications: Record<string, SupportedApplication> = {
  vscode: {
    name: "VS Code",
    scopes: ["workspace", "user"],
    transports: ["http", "stream", "stdio"],
  },
};

export const applicationKeys = Object.keys(supportedApplications);

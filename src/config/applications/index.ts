import { join } from "path";
import { homedir } from "os";

export interface ConfigPaths {
  workspace?: string;
  user?: string;
  global?: string;
}

export interface ConfigTemplate {
  servers?: Record<string, any>;
  mcpServers?: Record<string, any>;
}

export interface InstallationPaths {
  directories: string[];
  applications: string[];
  binaries: string[];
}

export interface SupportedApplication {
  name: string;
  scopes: ("workspace" | "user" | "global")[];
  transports: ("stdio" | "http" | "stream" | "sse")[];
  configPaths: ConfigPaths;
  templates: {
    stdio?: ConfigTemplate;
    http?: ConfigTemplate;
    sse?: ConfigTemplate;
  };
  installationPaths: InstallationPaths;
  configFormat: "vscode" | "standard" | "claude";
  notes?: string[];
}

function getPlatformSpecificPath(basePaths: Record<string, string>): string {
  const platform = process.platform;
  if (platform === "darwin") {
    return basePaths.darwin;
  } else if (platform === "win32") {
    return basePaths.win32;
  } else {
    return basePaths.linux;
  }
}

export const supportedApplications: Record<string, SupportedApplication> = {
  vscode: {
    name: "VS Code",
    scopes: ["workspace", "user"],
    transports: ["stdio", "http", "stream"],
    configPaths: {
      workspace: ".vscode/mcp.json",
      user: getPlatformSpecificPath({
        darwin: join(
          homedir(),
          "Library/Application Support/Code/User/settings.json"
        ),
        win32: join(homedir(), "AppData/Roaming/Code/User/settings.json"),
        linux: join(homedir(), ".config/Code/User/settings.json"),
      }),
    },
    templates: {
      stdio: {
        servers: {
          wcaiCli: {
            type: "stdio",
            command: "wcai-cli",
            args: ["serve", "stdio"],
            env: {
              WCAI_SCAN_DEPS: "true",
            },
          },
        },
      },
      http: {
        servers: {
          "mcp-wcai-http": {
            type: "http",
            url: "http://<host>:<port>/mcp",
          },
        },
      },
      sse: {
        servers: {
          "mcp-wcai-sse": {
            type: "sse",
            url: "http://<host>:<port>/sse",
          },
        },
      },
    },
    installationPaths: {
      directories: [join(homedir(), ".vscode")],
      applications: ["/Applications/Visual Studio Code.app"],
      binaries: ["/usr/bin/code", "/usr/local/bin/code"],
    },
    configFormat: "vscode",
    notes: [
      "VS Code format uses 'servers' key instead of 'mcpServers'",
      "Version >=1.101.0 will support automatic MCP server registration",
    ],
  },

  cursor: {
    name: "Cursor",
    scopes: ["workspace", "user"],
    transports: ["stdio", "http", "stream"],
    configPaths: {
      workspace: ".cursor/mcp.json",
      user: getPlatformSpecificPath({
        darwin: join(
          homedir(),
          "Library/Application Support/Cursor/User/settings.json"
        ),
        win32: join(homedir(), "AppData/Roaming/Cursor/User/settings.json"),
        linux: join(homedir(), ".config/Cursor/User/settings.json"),
      }),
    },
    templates: {
      stdio: {
        mcpServers: {
          wcaiCli: {
            type: "stdio",
            command: "wcai-cli",
            args: ["serve", "stdio"],
            env: {
              WCAI_SCAN_DEPS: "true",
            },
          },
        },
      },
      http: {
        mcpServers: {
          "mcp-wcai-http": {
            type: "http",
            url: "http://<host>:<port>/mcp",
          },
        },
      },
      sse: {
        mcpServers: {
          "mcp-wcai-sse": {
            type: "sse",
            url: "http://<host>:<port>/sse",
          },
        },
      },
    },
    installationPaths: {
      directories: [join(homedir(), ".cursor")],
      applications: ["/Applications/Cursor.app"],
      binaries: ["/usr/bin/cursor", "/usr/local/bin/cursor"],
    },
    configFormat: "standard",
  },

  claude: {
    name: "Claude Desktop",
    scopes: ["global"],
    transports: ["stdio"],
    configPaths: {
      global: getPlatformSpecificPath({
        darwin: join(
          homedir(),
          "Library/Application Support/Claude/claude_desktop_config.json"
        ),
        win32: join(
          homedir(),
          "AppData/Roaming/Claude/claude_desktop_config.json"
        ),
        linux: join(homedir(), ".config/Claude/claude_desktop_config.json"),
      }),
    },
    templates: {
      stdio: {
        mcpServers: {
          "wcai-cli": {
            command: "wcai-cli",
            args: ["serve", "stdio"],
            env: {
              WCAI_SCAN_DEPS: "true",
            },
          },
        },
      },
    },
    installationPaths: {
      directories: [],
      applications: ["/Applications/Claude.app"],
      binaries: [],
    },
    configFormat: "claude",
    notes: [
      "Claude Desktop only supports global configuration",
      "Uses simplified template format without 'type' field",
    ],
  },

  "claude-code": {
    name: "Claude Code",
    scopes: ["workspace"],
    transports: ["stdio", "http", "sse"],
    configPaths: {
      workspace: ".mcp.json",
    },
    templates: {
      stdio: {
        mcpServers: {
          "wcai-cli": {
            type: "stdio",
            command: "wcai-cli",
            args: ["serve", "stdio"],
            env: {
              WCAI_SCAN_DEPS: "true",
            },
          },
        },
      },
      http: {
        mcpServers: {
          "mcp-wcai-http": {
            type: "http",
            url: "http://<host>:<port>/mcp",
          },
        },
      },
      sse: {
        mcpServers: {
          "mcp-wcai-sse": {
            type: "sse",
            url: "http://<host>:<port>/sse",
          },
        },
      },
    },
    installationPaths: {
      directories: [],
      applications: [],
      binaries: ["claude"],
    },
    configFormat: "standard",
    notes: [
      "Project-scoped server configuration",
      "Can be generated using: claude mcp add --transport sse sse-server http://<host>:<port>/sse --scope project /path/to/your/project",
    ],
  },

  windsurf: {
    name: "Windsurf",
    scopes: ["workspace", "user"],
    transports: ["stdio", "http", "stream"],
    configPaths: {
      workspace: ".windsurf/mcp.json",
      user: getPlatformSpecificPath({
        darwin: join(homedir(), ".codeium/windsurf/mcp_config.json"),
        win32: join(homedir(), ".codeium/windsurf/mcp_config.json"),
        linux: join(homedir(), ".codeium/windsurf/mcp_config.json"),
      }),
    },
    templates: {
      stdio: {
        mcpServers: {
          wcaiCli: {
            type: "stdio",
            command: "wcai-cli",
            args: ["serve", "stdio"],
            env: {
              WCAI_SCAN_DEPS: "true",
            },
          },
        },
      },
      http: {
        mcpServers: {
          "mcp-wcai-http": {
            type: "http",
            url: "http://<host>:<port>/mcp",
          },
        },
      },
      sse: {
        mcpServers: {
          "mcp-wcai-sse": {
            type: "sse",
            url: "http://<host>:<port>/sse",
          },
        },
      },
    },
    installationPaths: {
      directories: [join(homedir(), ".windsurf")],
      applications: ["/Applications/Windsurf.app"],
      binaries: [],
    },
    configFormat: "standard",
  },

  trae: {
    name: "Trae",
    scopes: ["user"],
    transports: ["http"],
    configPaths: {
      user: "manual", // Configured through UI
    },
    templates: {
      stdio: {
        mcpServers: {
          "wcai-cli": {
            type: "stdio",
            command: "wcai-cli",
            args: ["serve", "stdio"],
            env: {
              WCAI_SCAN_DEPS: "true",
            },
          },
        },
      },
      http: {
        mcpServers: {
          "mcp-wcai-http": {
            type: "http",
            url: "http://localhost:3000/mcp",
          },
        },
      },
    },
    installationPaths: {
      directories: [],
      applications: [],
      binaries: [],
    },
    configFormat: "standard",
    notes: [
      "Only one MCP server entry is allowed at a time",
      "Configuration is done through the UI: Settings icon > MCP > + Add > Add Manually",
    ],
  },

  cline: {
    name: "Cline",
    scopes: ["workspace"],
    transports: ["stdio", "http", "sse"],
    configPaths: {
      workspace: "./cline_mcp_settings.json",
    },
    templates: {
      stdio: {
        mcpServers: {
          "wcai-cli": {
            type: "stdio",
            command: "wcai-cli",
            args: ["serve", "stdio"],
            env: {
              WCAI_SCAN_DEPS: "true",
            },
          },
        },
      },
      http: {
        mcpServers: {
          "mcp-wcai-http": {
            type: "http",
            url: "http://<host>:<port>/mcp",
          },
        },
      },
      sse: {
        mcpServers: {
          "mcp-wcai-sse": {
            type: "sse",
            url: "http://<host>:<port>/sse",
          },
        },
      },
    },
    installationPaths: {
      directories: [],
      applications: [],
      binaries: [],
    },
    configFormat: "standard",
    notes: [
      "Configuration through Cline extension: MCP Servers icon > Installed tab > Configure MCP Servers",
    ],
  },

  augment: {
    name: "Augment",
    scopes: ["user"],
    transports: ["stdio"],
    configPaths: {
      user: "settings.json", // Configured through VS Code settings.json
    },
    templates: {
      stdio: {
        // Augment uses a special format in VS Code settings.json
        // This is a placeholder - actual config goes in augment.advanced.mcpServers array
        mcpServers: [
          {
            name: "wcai-cli",
            command: "wcai-cli",
            args: ["serve", "stdio"],
            env: {
              WCAI_SCAN_DEPS: "true",
            },
          },
        ],
      },
    },
    installationPaths: {
      directories: [],
      applications: [],
      binaries: [],
    },
    configFormat: "standard",
    notes: [
      "Configure through Augment Settings Panel or VS Code settings.json",
      "MCP servers configured through one method are not visible in the other",
      "Uses array format in augment.advanced.mcpServers",
    ],
  },
};

export const applicationKeys = Object.keys(supportedApplications);

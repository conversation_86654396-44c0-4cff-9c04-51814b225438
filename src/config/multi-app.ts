/**
 * Multi-Application Configuration Manager
 * Handles configuration for <PERSON><PERSON> <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Windsurf, etc.
 */

import { readFileSync, writeFileSync, existsSync, mkdirSync } from "fs";
import { resolve, dirname } from "path";
import { supportedApplications } from "./applications/index.js";

export interface AppConfigResult {
  success: boolean;
  message: string;
  configPath?: string;
}

export interface AppConfigStatus {
  configured: boolean;
  message: string;
  configPath?: string;
}

export interface DiagnosticsResult {
  detectedApps: Record<string, boolean>;
  configStatus: Record<string, AppConfigStatus>;
  recommendations: string[];
}

export class MultiAppConfigManager {
  async initializeAppConfig(
    appName: string,
    options: {
      scope?: "workspace" | "user" | "global";
      force?: boolean;
    } = {}
  ): Promise<AppConfigResult> {
    const app = supportedApplications[appName];
    if (!app) {
      return {
        success: false,
        message: `Unknown application: ${appName}. Supported: ${Object.keys(
          supportedApplications
        ).join(", ")}`,
      };
    }

    const scope = options.scope || "workspace";
    let configPath: string;

    // Determine config path based on scope
    if (scope === "workspace" && app.configPaths.workspace) {
      configPath = resolve(app.configPaths.workspace);
    } else if (
      (scope === "user" || scope === "global") &&
      (app.configPaths.user || app.configPaths.global)
    ) {
      configPath = app.configPaths.user || app.configPaths.global!;
    } else {
      return {
        success: false,
        message: `${app.name} does not support ${scope} configuration`,
      };
    }

    // Check if config already exists
    if (existsSync(configPath) && !options.force) {
      return {
        success: false,
        message: `Configuration already exists at ${configPath}. Use --force to overwrite.`,
      };
    }

    try {
      // Create directory if needed
      const dir = dirname(configPath);
      if (!existsSync(dir)) {
        mkdirSync(dir, { recursive: true });
      }

      // Get the appropriate template (default to stdio)
      const template =
        app.templates.stdio || app.templates.http || app.templates.sse;
      if (!template) {
        return {
          success: false,
          message: `No template available for ${app.name}`,
        };
      }

      // Load existing config or create new one
      let config = template;
      if (existsSync(configPath)) {
        try {
          const existing = JSON.parse(readFileSync(configPath, "utf-8"));
          config = this.mergeConfigs(existing, template);
        } catch (error) {
          // If existing config is invalid, use template
          console.warn(`Warning: Invalid existing config, using template`);
        }
      }

      // Write config
      writeFileSync(configPath, JSON.stringify(config, null, 2));

      return {
        success: true,
        message: `Created ${app.name} configuration at ${configPath}`,
        configPath,
      };
    } catch (error) {
      return {
        success: false,
        message: `Failed to create configuration: ${
          error instanceof Error ? error.message : String(error)
        }`,
      };
    }
  }

  async getAppConfig(appName: string): Promise<any | null> {
    const app = supportedApplications[appName];
    if (!app) return null;

    // Try workspace config first, then user/global config
    const paths = [
      app.configPaths.workspace,
      app.configPaths.user,
      app.configPaths.global,
    ].filter(Boolean);

    for (const path of paths) {
      if (path && path !== "manual" && existsSync(path)) {
        try {
          return JSON.parse(readFileSync(path, "utf-8"));
        } catch (error) {
          console.warn(`Warning: Failed to parse ${path}`);
        }
      }
    }

    return null;
  }

  async getAllAppConfigs(): Promise<Record<string, any>> {
    const configs: Record<string, any> = {};

    for (const appName of Object.keys(supportedApplications)) {
      const config = await this.getAppConfig(appName);
      if (config) {
        configs[appName] = config;
      }
    }

    return configs;
  }

  async validateAppConfig(
    appName: string
  ): Promise<{ valid: boolean; errors: string[] }> {
    const config = await this.getAppConfig(appName);

    if (!config) {
      return {
        valid: false,
        errors: [`No configuration found for ${appName}`],
      };
    }

    const errors: string[] = [];

    // Basic validation - check for required fields
    if (appName === "claude") {
      if (!config.mcpServers) {
        errors.push("Missing mcpServers field");
      } else if (!config.mcpServers["wcai-cli"]) {
        errors.push("Missing wcai-cli server configuration");
      }
    } else {
      if (!config.servers) {
        errors.push("Missing servers field");
      } else if (!config.servers.wcaiCli && !config.servers["wcai-cli"]) {
        errors.push("Missing wcai-cli server configuration");
      }
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }

  async runDiagnostics(): Promise<DiagnosticsResult> {
    const detectedApps: Record<string, boolean> = {};
    const configStatus: Record<string, AppConfigStatus> = {};
    const recommendations: string[] = [];

    // Check which applications are detected/installed
    for (const [appName, app] of Object.entries(supportedApplications)) {
      detectedApps[appName] = await this.isAppInstalled(appName);

      const config = await this.getAppConfig(appName);
      const validation = await this.validateAppConfig(appName);

      if (config && validation.valid) {
        configStatus[appName] = {
          configured: true,
          message: "Configured and valid",
        };
      } else if (config && !validation.valid) {
        configStatus[appName] = {
          configured: false,
          message: `Configuration found but invalid: ${validation.errors.join(
            ", "
          )}`,
        };
      } else {
        configStatus[appName] = {
          configured: false,
          message: "Not configured",
        };

        if (detectedApps[appName]) {
          recommendations.push(
            `Configure wcai-cli for ${app.name} using: wcai-cli config init --app ${appName}`
          );
        }
      }
    }

    return {
      detectedApps,
      configStatus,
      recommendations,
    };
  }

  private async isAppInstalled(appName: string): Promise<boolean> {
    const app = supportedApplications[appName];
    if (!app) return false;

    // Check directories, applications, and binaries
    const pathsToCheck = [
      ...app.installationPaths.directories,
      ...app.installationPaths.applications,
      ...app.installationPaths.binaries,
    ];

    return pathsToCheck.some((path) => existsSync(path));
  }

  private mergeConfigs(existing: any, template: any): any {
    // Simple merge - preserve existing servers and add wcai-cli if not present
    const merged = { ...existing };

    if (template.servers && !merged.servers) {
      merged.servers = {};
    }

    if (template.mcpServers && !merged.mcpServers) {
      merged.mcpServers = {};
    }

    // Add wcai-cli server if not present
    if (
      template.servers?.wcaiCli &&
      !merged.servers?.wcaiCli &&
      !merged.servers?.["wcai-cli"]
    ) {
      merged.servers.wcaiCli = template.servers.wcaiCli;
    }

    if (template.mcpServers?.["wcai-cli"] && !merged.mcpServers?.["wcai-cli"]) {
      merged.mcpServers["wcai-cli"] = template.mcpServers["wcai-cli"];
    }

    return merged;
  }
}

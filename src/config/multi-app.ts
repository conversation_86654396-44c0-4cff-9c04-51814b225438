/**
 * Multi-Application Configuration Manager
 * Handles configuration for VS Code, <PERSON>urs<PERSON>, <PERSON>, Windsurf, etc.
 */

import { readFileSync, writeFileSync, existsSync, mkdirSync } from "fs";
import { join, resolve, dirname } from "path";
import { homedir } from "os";

export interface AppConfigResult {
  success: boolean;
  message: string;
  configPath?: string;
}

export interface AppConfigStatus {
  configured: boolean;
  message: string;
  configPath?: string;
}

export interface DiagnosticsResult {
  detectedApps: Record<string, boolean>;
  configStatus: Record<string, AppConfigStatus>;
  recommendations: string[];
}

export class MultiAppConfigManager {
  private readonly appConfigs = {
    vscode: {
      name: "VS Code",
      workspaceConfigPath: ".vscode/mcp.json",
      userConfigPath: this.getVSCodeUserConfigPath(),
      template: this.getVSCodeTemplate(),
    },
    cursor: {
      name: "<PERSON><PERSON><PERSON>",
      workspaceConfigPath: ".cursor/mcp.json",
      userConfigPath: this.getCursorUserConfigPath(),
      template: this.getCursorTemplate(),
    },
    claude: {
      name: "Claude Desktop",
      workspaceConfigPath: null, // Claude only has global config
      userConfigPath: this.getClaudeConfigPath(),
      template: this.getClaudeTemplate(),
    },
    windsurf: {
      name: "Windsurf",
      workspaceConfigPath: ".windsurf/mcp.json",
      userConfigPath: this.getWindsurfUserConfigPath(),
      template: this.getWindsurfTemplate(),
    },
  };

  async initializeAppConfig(
    appName: string,
    options: {
      scope?: "workspace" | "user" | "global";
      force?: boolean;
    } = {}
  ): Promise<AppConfigResult> {
    const app = this.appConfigs[appName as keyof typeof this.appConfigs];
    if (!app) {
      return {
        success: false,
        message: `Unknown application: ${appName}. Supported: ${Object.keys(
          this.appConfigs
        ).join(", ")}`,
      };
    }

    const scope = options.scope || "workspace";
    let configPath: string;

    // Determine config path based on scope
    if (scope === "workspace" && app.workspaceConfigPath) {
      configPath = resolve(app.workspaceConfigPath);
    } else if ((scope === "user" || scope === "global") && app.userConfigPath) {
      configPath = app.userConfigPath;
    } else {
      return {
        success: false,
        message: `${app.name} does not support ${scope} configuration`,
      };
    }

    // Check if config already exists
    if (existsSync(configPath) && !options.force) {
      return {
        success: false,
        message: `Configuration already exists at ${configPath}. Use --force to overwrite.`,
      };
    }

    try {
      // Create directory if needed
      const dir = dirname(configPath);
      if (!existsSync(dir)) {
        mkdirSync(dir, { recursive: true });
      }

      // Load existing config or create new one
      let config = app.template;
      if (existsSync(configPath)) {
        try {
          const existing = JSON.parse(readFileSync(configPath, "utf-8"));
          config = this.mergeConfigs(existing, app.template);
        } catch (error) {
          // If existing config is invalid, use template
          console.warn(`Warning: Invalid existing config, using template`);
        }
      }

      // Write config
      writeFileSync(configPath, JSON.stringify(config, null, 2));

      return {
        success: true,
        message: `Created ${app.name} configuration at ${configPath}`,
        configPath,
      };
    } catch (error) {
      return {
        success: false,
        message: `Failed to create configuration: ${
          error instanceof Error ? error.message : String(error)
        }`,
      };
    }
  }

  async getAppConfig(appName: string): Promise<any | null> {
    const app = this.appConfigs[appName as keyof typeof this.appConfigs];
    if (!app) return null;

    // Try workspace config first, then user config
    const paths = [app.workspaceConfigPath, app.userConfigPath].filter(Boolean);

    for (const path of paths) {
      if (path && existsSync(path)) {
        try {
          return JSON.parse(readFileSync(path, "utf-8"));
        } catch (error) {
          console.warn(`Warning: Failed to parse ${path}`);
        }
      }
    }

    return null;
  }

  async getAllAppConfigs(): Promise<Record<string, any>> {
    const configs: Record<string, any> = {};

    for (const appName of Object.keys(this.appConfigs)) {
      const config = await this.getAppConfig(appName);
      if (config) {
        configs[appName] = config;
      }
    }

    return configs;
  }

  async validateAppConfig(
    appName: string
  ): Promise<{ valid: boolean; errors: string[] }> {
    const config = await this.getAppConfig(appName);

    if (!config) {
      return {
        valid: false,
        errors: [`No configuration found for ${appName}`],
      };
    }

    const errors: string[] = [];

    // Basic validation - check for required fields
    if (appName === "claude") {
      if (!config.mcpServers) {
        errors.push("Missing mcpServers field");
      } else if (!config.mcpServers["wcai-cli"]) {
        errors.push("Missing wcai-cli server configuration");
      }
    } else {
      if (!config.servers) {
        errors.push("Missing servers field");
      } else if (!config.servers.wcaiCli && !config.servers["wcai-cli"]) {
        errors.push("Missing wcai-cli server configuration");
      }
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }

  async runDiagnostics(): Promise<DiagnosticsResult> {
    const detectedApps: Record<string, boolean> = {};
    const configStatus: Record<string, AppConfigStatus> = {};
    const recommendations: string[] = [];

    // Check which applications are detected/installed
    for (const [appName, app] of Object.entries(this.appConfigs)) {
      detectedApps[appName] = await this.isAppInstalled(appName);

      const config = await this.getAppConfig(appName);
      const validation = await this.validateAppConfig(appName);

      if (config && validation.valid) {
        configStatus[appName] = {
          configured: true,
          message: "Configured and valid",
        };
      } else if (config && !validation.valid) {
        configStatus[appName] = {
          configured: false,
          message: `Configuration found but invalid: ${validation.errors.join(
            ", "
          )}`,
        };
      } else {
        configStatus[appName] = {
          configured: false,
          message: "Not configured",
        };

        if (detectedApps[appName]) {
          recommendations.push(
            `Configure wcai-cli for ${app.name} using: wcai-cli config init --app ${appName}`
          );
        }
      }
    }

    return {
      detectedApps,
      configStatus,
      recommendations,
    };
  }

  private async isAppInstalled(appName: string): Promise<boolean> {
    // Simple heuristic - check if common directories exist
    switch (appName) {
      case "vscode":
        return (
          existsSync(join(homedir(), ".vscode")) ||
          existsSync("/Applications/Visual Studio Code.app") ||
          existsSync("/usr/bin/code")
        );
      case "cursor":
        return (
          existsSync(join(homedir(), ".cursor")) ||
          existsSync("/Applications/Cursor.app")
        );
      case "claude":
        return (
          existsSync(this.getClaudeConfigPath()) ||
          existsSync("/Applications/Claude.app")
        );
      case "windsurf":
        return (
          existsSync(join(homedir(), ".windsurf")) ||
          existsSync("/Applications/Windsurf.app")
        );
      default:
        return false;
    }
  }

  private mergeConfigs(existing: any, template: any): any {
    // Simple merge - preserve existing servers and add wcai-cli if not present
    const merged = { ...existing };

    if (template.servers && !merged.servers) {
      merged.servers = {};
    }

    if (template.mcpServers && !merged.mcpServers) {
      merged.mcpServers = {};
    }

    // Add wcai-cli server if not present
    if (
      template.servers?.wcaiCli &&
      !merged.servers?.wcaiCli &&
      !merged.servers?.["wcai-cli"]
    ) {
      merged.servers.wcaiCli = template.servers.wcaiCli;
    }

    if (template.mcpServers?.["wcai-cli"] && !merged.mcpServers?.["wcai-cli"]) {
      merged.mcpServers["wcai-cli"] = template.mcpServers["wcai-cli"];
    }

    return merged;
  }

  // Platform-specific config paths
  private getVSCodeUserConfigPath(): string {
    const platform = process.platform;
    if (platform === "darwin") {
      return join(
        homedir(),
        "Library/Application Support/Code/User/settings.json"
      );
    } else if (platform === "win32") {
      return join(homedir(), "AppData/Roaming/Code/User/settings.json");
    } else {
      return join(homedir(), ".config/Code/User/settings.json");
    }
  }

  private getCursorUserConfigPath(): string {
    const platform = process.platform;
    if (platform === "darwin") {
      return join(
        homedir(),
        "Library/Application Support/Cursor/User/settings.json"
      );
    } else if (platform === "win32") {
      return join(homedir(), "AppData/Roaming/Cursor/User/settings.json");
    } else {
      return join(homedir(), ".config/Cursor/User/settings.json");
    }
  }

  private getClaudeConfigPath(): string {
    const platform = process.platform;
    if (platform === "darwin") {
      return join(
        homedir(),
        "Library/Application Support/Claude/claude_desktop_config.json"
      );
    } else if (platform === "win32") {
      return join(
        homedir(),
        "AppData/Roaming/Claude/claude_desktop_config.json"
      );
    } else {
      return join(homedir(), ".config/Claude/claude_desktop_config.json");
    }
  }

  private getWindsurfUserConfigPath(): string {
    const platform = process.platform;
    if (platform === "darwin") {
      return join(
        homedir(),
        "Library/Application Support/Windsurf/User/settings.json"
      );
    } else if (platform === "win32") {
      return join(homedir(), "AppData/Roaming/Windsurf/User/settings.json");
    } else {
      return join(homedir(), ".config/Windsurf/User/settings.json");
    }
  }

  // Configuration templates
  private getVSCodeTemplate() {
    return {
      servers: {
        wcaiCli: {
          type: "stdio",
          command: "wcai-cli",
          args: ["serve", "stdio"],
          env: {
            WCAI_SCAN_DEPS: "true",
          },
        },
      },
    };
  }

  private getCursorTemplate() {
    return {
      mcpServers: {
        wcaiCli: {
          type: "stdio",
          command: "wcai-cli",
          args: ["serve", "stdio"],
          env: {
            WCAI_SCAN_DEPS: "true",
          },
        },
      },
    };
  }

  private getClaudeTemplate() {
    return {
      mcpServers: {
        "wcai-cli": {
          command: "wcai-cli",
          args: ["serve", "stdio"],
          env: {
            WCAI_SCAN_DEPS: "true",
          },
        },
      },
    };
  }

  private getWindsurfTemplate() {
    return {
      servers: {
        wcaiCli: {
          type: "stdio",
          command: "wcai-cli",
          args: ["serve", "stdio"],
          env: {
            WCAI_SCAN_DEPS: "true",
          },
        },
      },
    };
  }
}

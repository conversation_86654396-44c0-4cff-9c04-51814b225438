/**
 * Core types for wcai-cli - Web Component AI Tools
 */

// Re-export Custom Elements Manifest types from the official package
export type {
  Package as CustomElementsManifest,
  Module,
  Declaration,
  ClassDeclaration,
  CustomElementDeclaration,
  Attribute,
  Event,
  Slot,
  CssPart,
  CssCustomProperty,
  ClassMember,
  Type,
  Reference,
  Export,
} from "custom-elements-manifest";

// Configuration types
export interface WcaiConfig {
  discovery: DiscoveryConfig;
  servers: Record<string, ServerConfig>;
  applications?: ApplicationConfig;
}

export interface DiscoveryConfig {
  scanDependencies: boolean;
  manifestPaths: string[];
  excludePackages: string[];
  includeDevDependencies: boolean;
  cacheEnabled: boolean;
  cacheTtl: number;
}

export interface ServerConfig {
  transport: "stdio" | "http" | "stream";
  port?: number;
  host?: string;
  tools: string[];
  resources: string[];
  prompts: string[];
}

export interface ApplicationConfig {
  vscode?: AppSpecificConfig;
  cursor?: AppSpecificConfig;
  claude?: AppSpecificConfig;
  windsurf?: AppSpecificConfig;
}

export interface AppSpecificConfig {
  enabled: boolean;
  configPath?: string;
  scope: "workspace" | "user" | "global";
}

// Component registry types
export interface ComponentRegistry {
  components: Map<string, ComponentInfo>;
  libraries: Map<string, LibraryInfo>;
  lastUpdated: Date;
}

export interface ComponentInfo {
  tagName: string;
  className: string;
  module: string;
  package: string;
  version: string;
  manifest: any; // Using any for now to handle CEM utilities component type
  library?: string;
  framework?: "lit" | "stencil" | "vanilla" | "angular" | "vue";
}

export interface LibraryInfo {
  name: string;
  version: string;
  manifestPath: string;
  components: string[];
  framework?: string;
  description?: string;
}

// CLI command types
export interface CLIOptions {
  config?: string;
  verbose?: boolean;
  quiet?: boolean;
  app?: string | string[];
  scope?: "workspace" | "user" | "global";
  transport?: "stdio" | "http" | "stream";
  port?: number;
  host?: string;
  watch?: boolean;
  scanDeps?: boolean;
}

// MCP tool parameter types
export interface ListComponentsParams {
  library?: string;
  framework?: string;
  search?: string;
  limit?: number;
  offset?: number;
}

export interface GetComponentInfoParams {
  tagName?: string;
  className?: string;
  includeExamples?: boolean;
  includeUsage?: boolean;
}

export interface SearchComponentsParams {
  query: string;
  library?: string;
  framework?: string;
  attributes?: string[];
  events?: string[];
  limit?: number;
}

export interface ValidateUsageParams {
  filePath?: string;
  content?: string;
  tagName?: string;
}

export interface GenerateExampleParams {
  tagName: string;
  framework?: "html" | "lit" | "react" | "vue" | "angular";
  includeStyles?: boolean;
  includeEvents?: boolean;
}

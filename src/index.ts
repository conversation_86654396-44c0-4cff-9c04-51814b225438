/**
 * wcai-cli - Web Component AI Tools
 * Main library exports for programmatic usage
 */

// Export all types
export * from './types/index.js';

// Export core classes and utilities
export { ConfigManager } from './config/manager.js';
export { ComponentRegistry } from './discovery/registry.js';

// Export MCP setup functions
export { setupMCPTools } from './mcp/tools.js';
export { setupMCPResources } from './mcp/resources.js';
export { setupMCPPrompts } from './mcp/prompts.js';

// Export command handlers for programmatic usage
export { serveCommand } from './commands/serve.js';
export { configCommand } from './commands/config.js';
export { discoverCommand } from './commands/discover.js';
export { devCommand } from './commands/dev.js';
export { infoCommand } from './commands/info.js';
export { inspectCommand } from './commands/inspect.js';
export { validateCommand } from './commands/validate.js';

// Export multi-app configuration utilities
export { MultiAppConfigManager } from './config/multi-app.js';

// Re-export commonly used external dependencies for convenience
export { FastMCP } from 'fastmcp';
export { z } from 'zod';

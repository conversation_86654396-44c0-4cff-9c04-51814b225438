# wcai-cli - Web Component AI Tools

A powerful MCP (Model Context Protocol) server CLI that provides AI agents with comprehensive information about web components and custom elements in your workspace.

## Features

### 🔍 Component Discovery

- **Custom Elements Manifest (CEM) Discovery**: Automatically finds and parses Custom Elements Manifest files from your workspace and dependencies
- **Package.json Integration**: Discovers components via `customElements` entries in package.json files
- **Multi-Library Support**: Works with Lit, Stencil, vanilla web components, and more
- **Dependency Scanning**: Optionally scans node_modules for component libraries

### 🤖 MCP Server Capabilities

- **Tools**: Component listing, search, detailed info, usage validation, and example generation
- **Resources**: Component documentation, library information, usage examples, and API references
- **Prompts**: AI-assisted component creation, debugging, migration, and optimization

### ⚙️ Multi-Application Configuration

- **VS Code**: `.vscode/mcp.json` and `settings.json` support
- **Cursor**: `.cursor/mcp.json` configuration
- **Claude Desktop**: Global `claude_desktop_config.json` setup
- **Claude Code**: Project-scoped `.mcp.json` configuration
- **Windsurf**: `.windsurf/mcp.json` and global `mcp_config.json` support
- **Trae**: Manual UI configuration support
- **Cline**: `./cline_mcp_settings.json` configuration
- **Augment**: `.augment/mcp.json` configuration (STDIO support)

## Installation

```bash
npm install -g wcai-cli
# or
pnpm add -g wcai-cli
```

## Quick Start

### 1. Discover Components

```bash
# Discover components in current workspace
wcai-cli discover

# Include dependencies
wcai-cli discover --scan-deps

# Filter by library or framework
wcai-cli discover --library my-components --framework lit
```

### 2. Configure for AI Applications

```bash
# Configure for VS Code
wcai-cli config init --app vscode

# Configure for multiple applications
wcai-cli config init --app vscode cursor claude windsurf

# Configure Claude Desktop (global scope)
wcai-cli config init --app claude --scope global

# Configure Claude Code (project scope)
wcai-cli config init --app claude-code

# Configure Cline extension
wcai-cli config init --app cline

# Configure Augment (workspace scope)
wcai-cli config init --app augment

# Check configuration status
wcai-cli config doctor
```

### 3. Start MCP Server

```bash
# Start stdio server (for most MCP clients)
wcai-cli serve stdio

# Start HTTP server for development
wcai-cli serve http --port 8080

# Start with custom configuration
wcai-cli serve stdio --config .wcai-cli.json
```

## CLI Commands

### Server Commands

```bash
wcai-cli serve stdio              # Start stdio MCP server
wcai-cli serve http [--port 8080] # Start HTTP/SSE MCP server
wcai-cli serve stream             # Start HTTP Stream MCP server
```

### Discovery Commands

```bash
wcai-cli discover                 # Scan workspace for components
wcai-cli list [--format json]     # List discovered components
wcai-cli info <component-name>    # Show component details
```

### Configuration Commands

```bash
wcai-cli config init --app <apps...>     # Initialize app configurations
wcai-cli config show [--app <name>]      # Show configuration
wcai-cli config validate [--app <name>]  # Validate configuration
wcai-cli config doctor                   # Diagnose configuration issues
```

### Development Commands

```bash
wcai-cli dev [--watch]            # Development mode with auto-reload
wcai-cli inspect <manifest-file>  # Inspect CEM file contents
wcai-cli validate [file]          # Validate component usage
```

## Configuration

### Application-Specific Configuration

**VS Code (`.vscode/mcp.json`)**:

```json
{
  "servers": {
    "wcaiCli": {
      "type": "stdio",
      "command": "wcai-cli",
      "args": ["serve", "stdio"],
      "env": {
        "WCAI_SCAN_DEPS": "true"
      }
    }
  }
}
```

**Claude Desktop (`claude_desktop_config.json`)**:

```json
{
  "mcpServers": {
    "wcai-cli": {
      "command": "wcai-cli",
      "args": ["serve", "stdio"],
      "env": {
        "WCAI_SCAN_DEPS": "true"
      }
    }
  }
}
```

### Generic Configuration (`.wcai-cli.json`)

```json
{
  "discovery": {
    "scanDependencies": true,
    "manifestPaths": ["./custom-elements.json"],
    "excludePackages": ["@types/*"],
    "includeDevDependencies": false
  },
  "servers": {
    "default": {
      "transport": "stdio",
      "tools": ["list-components", "get-component-info"],
      "resources": ["component-docs", "usage-examples"],
      "prompts": ["create-component", "debug-component"]
    }
  }
}
```

## MCP Tools

### `list-components`

List all discovered web components with optional filtering.

**Parameters:**

- `library` (optional): Filter by library name
- `framework` (optional): Filter by framework (lit, stencil, vanilla, etc.)
- `search` (optional): Search query for component names/descriptions
- `limit` (optional): Maximum number of components to return

### `get-component-info`

Get detailed information about a specific web component.

**Parameters:**

- `tagName` (optional): Component tag name (e.g., "my-button")
- `className` (optional): Component class name (e.g., "MyButton")
- `includeExamples` (optional): Include usage examples
- `includeUsage` (optional): Include usage patterns

### `search-components`

Search for web components using various criteria.

**Parameters:**

- `query` (required): Search query
- `library` (optional): Filter by library name
- `framework` (optional): Filter by framework
- `attributes` (optional): Filter by required attributes
- `events` (optional): Filter by events

### `generate-example`

Generate usage examples for a web component.

**Parameters:**

- `tagName` (required): Component tag name
- `framework` (optional): Target framework (html, lit, react, vue, angular)
- `includeStyles` (optional): Include CSS styling examples
- `includeEvents` (optional): Include event handling examples

## MCP Resources

- `component-docs://list` - List of all component documentation
- `component-docs://{tagName}` - Detailed component documentation
- `library-info://list` - List of all discovered libraries
- `library-info://{libraryName}` - Detailed library information
- `usage-examples://list` - List of usage examples
- `usage-examples://{tagName}` - Component usage examples
- `api-reference://{tagName}` - Component API reference

## MCP Prompts

### `create-component`

Help create a new web component based on requirements.

**Arguments:**

- `name` (required): Component name
- `framework` (optional): Framework to use (lit, stencil, vanilla)
- `features` (optional): Required features (comma-separated)
- `similar` (optional): Similar existing component for reference

### `debug-component`

Help debug issues with a web component.

**Arguments:**

- `tagName` (required): Component tag name to debug
- `issue` (required): Description of the issue
- `code` (optional): Relevant code snippet
- `browser` (optional): Browser where issue occurs

### `migrate-component`

Help migrate a component from one framework to another.

**Arguments:**

- `tagName` (required): Component to migrate
- `fromFramework` (required): Current framework
- `toFramework` (required): Target framework
- `preserveAPI` (optional): Whether to preserve existing API

### `optimize-component`

Help optimize a web component for performance and best practices.

**Arguments:**

- `tagName` (required): Component to optimize
- `focus` (optional): Optimization focus (performance, accessibility, bundle-size, maintainability)

## Environment Variables

- `WCAI_SCAN_DEPS`: Enable/disable dependency scanning (true/false)
- `WCAI_MANIFEST_PATH`: Additional manifest file paths (comma-separated)
- `WCAI_EXCLUDE_PACKAGES`: Packages to exclude (comma-separated)
- `WCAI_TRANSPORT`: Default transport type (stdio/http/stream)
- `WCAI_PORT`: Default port for HTTP transport
- `WCAI_HOST`: Default host for HTTP transport

## Custom Elements Manifest Support

wcai-cli uses the official Custom Elements Manifest format and the `@wc-toolkit/cem-utilities` library for component discovery and analysis. It supports:

- Component attributes, events, slots, CSS properties, and CSS parts
- Multiple frameworks (Lit, Stencil, vanilla, Angular Elements, Vue)
- TypeScript type information
- JSDoc documentation
- Deprecation warnings

## Development

```bash
# Clone the repository
git clone <repository-url>
cd wcai-cli

# Install dependencies
pnpm install

# Build the project
pnpm build

# Run in development mode
pnpm dev

# Test the CLI
node dist/cli.js --help
```

## License

ISC

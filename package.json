{"name": "wcai-cli", "version": "0.0.3", "description": "Web Component AI Tools - Standalone MCP CLI Server", "keywords": ["mcp", "model-context-protocol", "web-components", "custom-elements", "ai", "cli"], "author": "", "license": "MIT", "type": "module", "main": "dist/index.js", "bin": {"wcai-cli": "dist/cli.js"}, "scripts": {"build": "tsc", "dev": "tsc --watch", "start": "node dist/cli.js", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"@inquirer/prompts": "^7.5.3", "@wc-toolkit/cem-utilities": "^1.3.0", "chalk": "^5.4.1", "commander": "^14.0.0", "custom-elements-manifest": "^2.1.0", "fastmcp": "^3.5.0", "glob": "^11.0.3", "zod": "^3.25.67"}, "devDependencies": {"@types/node": "^20.0.0", "typescript": "^5.0.0"}, "packageManager": "pnpm@10.11.0"}
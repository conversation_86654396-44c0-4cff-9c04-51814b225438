{"schemaVersion": "1.0.0", "readme": "Sample web components for testing wcai-cli", "modules": [{"kind": "javascript-module", "path": "./src/my-button.js", "declarations": [{"kind": "class", "description": "A customizable button component", "name": "MyButton", "tagName": "my-button", "customElement": true, "attributes": [{"name": "variant", "type": {"text": "string"}, "description": "Button variant (primary, secondary, danger)", "default": "primary"}, {"name": "disabled", "type": {"text": "boolean"}, "description": "Whether the button is disabled", "default": "false"}], "events": [{"name": "button-click", "type": {"text": "CustomEvent"}, "description": "Fired when the button is clicked"}], "slots": [{"name": "", "description": "Default slot for button content"}], "cssProperties": [{"name": "--button-color", "description": "Primary color of the button", "default": "#007bff"}], "cssParts": [{"name": "button", "description": "The button element"}]}], "exports": [{"kind": "js", "name": "MyButton", "declaration": {"name": "MyButton", "module": "./src/my-button.js"}}, {"kind": "custom-element-definition", "name": "my-button", "declaration": {"name": "MyButton", "module": "./src/my-button.js"}}]}]}